# rAthena Server Dockerfile
FROM ubuntu:22.04

# Prevent interactive prompts during package installation
ENV DEBIAN_FRONTEND=noninteractive

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    git \
    zlib1g-dev \
    libmysqlclient-dev \
    libpcre3-dev \
    libssl-dev \
    wget \
    curl \
    nano \
    vim \
    mysql-client \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Create rathena user
RUN useradd -m -s /bin/bash rathena

# Set working directory
WORKDIR /rathena

# Clone rAthena repository (if not mounted)
RUN if [ ! -f "/rathena/configure" ]; then \
        git clone https://github.com/rathena/rathena.git . && \
        chown -R rathena:rathena /rathena; \
    fi

# Switch to rathena user
USER rathena

# Configure and compile rAthena with packet version 20190605
RUN ./configure --enable-epoll=yes --enable-prere=yes --enable-vip=yes --enable-packetver=20190605 && \
    make clean && \
    make -j8 server

# Create necessary directories
RUN mkdir -p /rathena/log /rathena/save

# Copy startup script
COPY --chown=rathena:rathena start.sh /rathena/start.sh
RUN chmod +x /rathena/start.sh

# Expose ports
EXPOSE 6900 6121 5121

# Start the servers
CMD ["/rathena/start.sh"]
