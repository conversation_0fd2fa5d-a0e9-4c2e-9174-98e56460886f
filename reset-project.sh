#!/bin/bash

# RagnaDocker Project Reset Script
# This script performs a "factory reset" of the RagnaDocker project
# WARNING: This will delete all downloaded source code, database data, and Docker artifacts

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Project name for Docker resources
PROJECT_NAME="ragnadocker"

echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${BLUE}║                    RagnaDocker Factory Reset                 ║${NC}"
echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${NC}"
echo ""

# Function to print section headers
print_section() {
    echo -e "${BLUE}▶ $1${NC}"
}

# Function to print warnings
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Function to print success
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Function to print error
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Check if we're in the right directory
if [ ! -f "docker-compose.yml" ] || [ ! -f "README.md" ]; then
    print_error "This script must be run from the RagnaDocker project root directory"
    exit 1
fi

# Display what will be deleted
echo -e "${YELLOW}This script will perform the following actions:${NC}"
echo ""
echo "🗑️  Remove downloaded source code:"
echo "   • rAthena source files (keeping Dockerfile and configs)"
echo "   • FluxCP source files (keeping Dockerfile and configs)"
echo ""
echo "🗑️  Reset database data:"
echo "   • All MariaDB data and volumes"
echo "   • All database backups in sql/ directory"
echo ""
echo "🗑️  Clean Docker artifacts:"
echo "   • Stop and remove all project containers"
echo "   • Remove project Docker images"
echo "   • Remove project Docker volumes"
echo "   • Remove project Docker networks"
echo ""
echo "🗑️  Clean logs and temporary files:"
echo "   • All log files in logs/ directory"
echo "   • Temporary files and caches"
echo ""
echo -e "${GREEN}✅ Preserve project structure:${NC}"
echo "   • docker-compose.yml"
echo "   • Makefile and README.md"
echo "   • Custom Dockerfiles and configurations"
echo "   • Project scripts and documentation"
echo ""

# Confirmation prompt
print_warning "This action is IRREVERSIBLE and will delete all your data!"
print_warning "Make sure you have backed up any important data before proceeding."
echo ""
read -p "Are you sure you want to reset the project? Type 'yes' to confirm: " -r
echo ""

if [[ ! $REPLY =~ ^yes$ ]]; then
    print_error "Reset cancelled by user"
    exit 1
fi

echo -e "${RED}Final confirmation: This will DELETE ALL DATA!${NC}"
read -p "Type 'RESET' in uppercase to proceed: " -r
echo ""

if [[ ! $REPLY =~ ^RESET$ ]]; then
    print_error "Reset cancelled - incorrect confirmation"
    exit 1
fi

print_success "Starting factory reset..."
echo ""

# Step 1: Stop and remove Docker containers
print_section "Step 1: Stopping and removing Docker containers"
if docker-compose ps -q | grep -q .; then
    docker-compose down --remove-orphans
    print_success "Containers stopped and removed"
else
    print_success "No running containers found"
fi

# Step 2: Remove Docker images
print_section "Step 2: Removing Docker images"
IMAGES=$(docker images --filter "reference=${PROJECT_NAME}*" -q)
if [ ! -z "$IMAGES" ]; then
    docker rmi $IMAGES --force
    print_success "Project Docker images removed"
else
    print_success "No project Docker images found"
fi

# Step 3: Remove Docker volumes
print_section "Step 3: Removing Docker volumes"
VOLUMES=$(docker volume ls --filter "name=${PROJECT_NAME}" -q)
if [ ! -z "$VOLUMES" ]; then
    docker volume rm $VOLUMES --force
    print_success "Project Docker volumes removed"
else
    print_success "No project Docker volumes found"
fi

# Step 4: Remove Docker networks
print_section "Step 4: Removing Docker networks"
NETWORKS=$(docker network ls --filter "name=${PROJECT_NAME}" -q)
if [ ! -z "$NETWORKS" ]; then
    docker network rm $NETWORKS
    print_success "Project Docker networks removed"
else
    print_success "No project Docker networks found"
fi

# Step 5: Clean rAthena directory
print_section "Step 5: Cleaning rAthena directory"
if [ -d "rathena" ]; then
    # Preserve our custom files
    mkdir -p /tmp/rathena_backup
    [ -f "rathena/Dockerfile" ] && cp "rathena/Dockerfile" /tmp/rathena_backup/
    [ -f "rathena/start.sh" ] && cp "rathena/start.sh" /tmp/rathena_backup/
    
    # Remove all content
    rm -rf rathena/*
    
    # Restore our custom files
    [ -f "/tmp/rathena_backup/Dockerfile" ] && cp /tmp/rathena_backup/Dockerfile rathena/
    [ -f "/tmp/rathena_backup/start.sh" ] && cp /tmp/rathena_backup/start.sh rathena/
    
    # Cleanup
    rm -rf /tmp/rathena_backup
    
    print_success "rAthena directory cleaned (custom files preserved)"
else
    print_success "rAthena directory not found"
fi

# Step 6: Clean FluxCP directory
print_section "Step 6: Cleaning FluxCP directory"
if [ -d "fluxcp" ]; then
    # Preserve our custom files
    mkdir -p /tmp/fluxcp_backup
    [ -f "fluxcp/Dockerfile" ] && cp "fluxcp/Dockerfile" /tmp/fluxcp_backup/
    [ -f "fluxcp/Dockerfile.simple" ] && cp "fluxcp/Dockerfile.simple" /tmp/fluxcp_backup/
    [ -f "fluxcp/apache-config.conf" ] && cp "fluxcp/apache-config.conf" /tmp/fluxcp_backup/
    [ -f "fluxcp/php.ini" ] && cp "fluxcp/php.ini" /tmp/fluxcp_backup/
    
    # Remove all content
    rm -rf fluxcp/*
    
    # Restore our custom files
    [ -f "/tmp/fluxcp_backup/Dockerfile" ] && cp /tmp/fluxcp_backup/Dockerfile fluxcp/
    [ -f "/tmp/fluxcp_backup/Dockerfile.simple" ] && cp /tmp/fluxcp_backup/Dockerfile.simple fluxcp/
    [ -f "/tmp/fluxcp_backup/apache-config.conf" ] && cp /tmp/fluxcp_backup/apache-config.conf fluxcp/
    [ -f "/tmp/fluxcp_backup/php.ini" ] && cp /tmp/fluxcp_backup/php.ini fluxcp/
    
    # Cleanup
    rm -rf /tmp/fluxcp_backup
    
    print_success "FluxCP directory cleaned (custom files preserved)"
else
    print_success "FluxCP directory not found"
fi

# Step 7: Clean logs directory
print_section "Step 7: Cleaning logs directory"
if [ -d "logs" ]; then
    rm -rf logs/*
    print_success "Logs directory cleaned"
else
    print_success "Logs directory not found"
fi

# Step 8: Clean SQL backups
print_section "Step 8: Cleaning SQL backups"
if [ -d "sql" ]; then
    find sql/ -name "*.sql" -not -name "01-init-database.sql" -delete
    print_success "SQL backups cleaned (init script preserved)"
else
    print_success "SQL directory not found"
fi

# Step 9: Clean Docker system (optional)
print_section "Step 9: Docker system cleanup"
docker system prune -f --volumes
print_success "Docker system cleaned"

echo ""
print_success "Factory reset completed successfully!"
echo ""
echo -e "${GREEN}╔══════════════════════════════════════════════════════════════╗${NC}"
echo -e "${GREEN}║                     Reset Complete!                          ║${NC}"
echo -e "${GREEN}╚══════════════════════════════════════════════════════════════╝${NC}"
echo ""
echo -e "${BLUE}Next steps to rebuild the environment:${NC}"
echo "1. Run: ${YELLOW}./start.sh${NC} (automatic setup)"
echo "   OR"
echo "2. Run: ${YELLOW}make setup${NC} (manual setup)"
echo ""
echo -e "${BLUE}This will:${NC}"
echo "• Download rAthena and FluxCP source code"
echo "• Build Docker images"
echo "• Start all services"
echo "• Initialize the database"
echo ""
echo -e "${GREEN}Your project is now in factory-fresh state! 🎉${NC}"
