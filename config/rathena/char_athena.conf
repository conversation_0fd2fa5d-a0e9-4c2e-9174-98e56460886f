// rAthena Character Server Configuration

// Server name, ideally you want to make it unique.
server_name: rAthena

// Wisp name for server: used to send wisp from server to players (between 4 to 23 characters)
wisp_server_name: Server

// Login Server IP
// The character server connects to the login server using this IP address.
// NOTE: This is useful when you are running behind a firewall or are on
// a machine with multiple interfaces.
login_ip: rathena

// The character server listens on the interface with this IP address.
// NOTE: This allows you to run multiple servers on multiple interfaces
// while using the same ports for each server.
bind_ip: 0.0.0.0

// Login Server Port
login_port: 6900

// Character Server IP
// The IP address which clients will use to connect.
// Set this to what your server's public IP address is.
char_ip: 127.0.0.1

// Character Server Port
char_port: 6121

// Password for inter-server communication
// NOTE: If you enable the inter-server communication to use a password,
// you must set a password for communication, or the servers will not be able to connect to each other!
// The password is case sensitive.
// NOTE: Do not use spaces or any of these characters which are not allowed in passwords: \ " ' ` / ? < > , . ; : [ ] { } | = + - _ ) ( * & ^ % $ # @ ! ~
//passwd: p1

// Character server name, ideally you want to make it unique.
// NOTE: Do not use spaces or any of these characters which are not allowed in server names: \ " ' ` / ? < > , . ; : [ ] { } | = + - _ ) ( * & ^ % $ # @ ! ~
server_name: rAthena

// Wisp name for server: used to send wisp from server to players (between 4 to 23 characters)
wisp_server_name: Server

// Guild earned exp modifier.
// Adjusts taxed exp before adding it to the guild's exp. For example, if set 
// to 200, the guild receives double the player's taxed exp.
guild_exp_rate: 100

// Name used on all @who commands to denote normal players.
// (Note that this value is the one that gets saved in the 'user_pass' field,
// hence why it needs to be 39 characters or less in length)
unknown_char_name: Unknown

// Choose if the char server is allowed to request the registry data from the login server (account variables)
// Don't change this unless you know what you're doing.
request_accreg2: 1

// Starting point for new characters
// Format: map_name,x,y
start_point: new_1-1,53,111

// Starting point for new characters when the specified map from start_point is not available (note: Has no effect if 'start_point' map is available)
// Format: map_name,x,y  
start_point_pre: training_1,18,27

// Starting items for new characters
// Format: id,amount,position,... (max 32 items)
// Position: 0 = non-equipped, 1 = Lower Headgear, 2 = Weapon, 4 = Garment, 8 = Accessory 1, 16 = Armor, 32 = Shield, 64 = Upper Headgear, 128 = Accessory 2, 256 = Lower Garment, 512 = Shoes, 1024 = Glove, 2048 = Middle Headgear, 4096 = Costume Upper Headgear, 8192 = Costume Middle Headgear, 16384 = Costume Lower Headgear, 32768 = Costume Garment/Robe, 65536 = Costume Shoes, 131072 = Costume Accessory 1, 262144 = Costume Accessory 2, 524288 = Costume Accessory 3, 1048576 = Shadow Armor, 2097152 = Shadow Weapon, 4194304 = Shadow Shield, 8388608 = Shadow Shoes, 16777216 = Shadow Accessory 2, 33554432 = Shadow Accessory 1
start_items: 1201,1,2,2301,1,16,5055,1,64

// Starting zeny for new characters
start_zeny: 0

// Size for the fame-lists
fame_list_alchemist: 10
fame_list_blacksmith: 10
fame_list_taekwon: 10

// Guild castle ownership and fortress investment feature
// NOTE: Requires updated guild scripts.
guild_castle_invite: 0
guild_castle_expulsion: 0

// Pincode system
// NOTE: Requires client 2011-03-09aragexeRE or newer.
// A window is opened before you can select your character and you will have to enter a pincode by using only your mouse.
// Default: 0 (disabled)
// State: 0 = disabled, 1 = enabled
pincode_enabled: 0

// Request Pincode only on login or on every character selection?
// Default: 0 (on every character selection)
// State: 0 = on every character selection, 1 = only on login
pincode_request: 0

// How often does a user have to change his pincode?
// Default: 0 (never)
// State: 0 = never, X = every X days
pincode_changetime: 0

// How often can a user enter the wrong pincode?
// Default: 3 (3 tries)
// State: 0 = unlimited, X = up to X tries (client maximum is 3)
pincode_maxtry: 3

// Are users forced to use a pincode when the system is enabled?
// Default: 1 (yes)
// State: 0 = no, 1 = yes
pincode_force: 1

import: conf/inter_athena.conf
import: conf/import/char_conf.txt
