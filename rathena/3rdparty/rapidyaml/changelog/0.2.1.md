This release is focused on bug fixes and compliance with the [YAML test suite](https://github.com/yaml/yaml-test-suite).

### Breaking changes

- Fix parsing behavior of root-level scalars: now these are parsed into a DOCVAL, not SEQ->VAL ([5ba0d56](https://github.com/biojppm/rapidyaml/pull/144/commits/5ba0d56904daef1509f0073695145c4835ab1b30), from [PR #144](https://github.com/biojppm/rapidyaml/pull/144)). Eg,
  ```yaml
  ---
  this is a scalar
  --- # previously this was parsed as
  - this is a scalar
  ```
- Cleanup type predicate API ([PR #155](https://github.com/biojppm/rapidyaml/pull/155))):
  - ensure all type predicates from `Tree` and `NodeRef` forward to the corresponding predicate in `NodeType`
  - remove all type predicates and methods from `NodeData`; use the equivalent call from `Tree` or `NodeRef`. For example, for `is_map()`:
    ```c++
    Tree t = parse("{foo: bar}");
    size_t map_id = t.root_id();
    NodeRef map = t.rootref();
    t.get(map_id)->is_map(); // compile error: no longer exists
    assert(t.is_map(map_id)); // OK
    assert(map.is_map()); // OK
    ```
  - Further cleanup to the type predicate API will be done in the future, especially around the `.has_*()` vs corresponding `.is_*()` naming scheme.


### New features & improvements

- `Tree::lookup_path_or_modify()`: add overload to graft existing branches ([PR #141](https://github.com/biojppm/rapidyaml/pull/141))
- Callbacks: improve test coverage ([PR #141](https://github.com/biojppm/rapidyaml/pull/141))
- [YAML test suite](https://github.com/yaml/yaml-test-suite) ([PR #144](https://github.com/biojppm/rapidyaml/pull/144), [PR #145](https://github.com/biojppm/rapidyaml/pull/145)): big progress towards compliance with the suite. There are still a number of existing problems, which are the subject of ongoing work. See the [list of current known failures](../test/test_suite/test_suite_parts.cpp) in the test suite file.
- Python wheels and source package are now [uploaded to PyPI](https://pypi.org/project/rapidyaml/) as part of the release process.


### Fixes

#### Anchors and references
- Fix resolving of nodes with keyref+valref ([PR #144](https://github.com/biojppm/rapidyaml/pull/144)): `{&a a: &b b, *b: *a}`
- Fix parsing of implicit scalars when tags are present ([PR #145](https://github.com/biojppm/rapidyaml/pull/145)):
  ```yaml
  - &a  # test case PW8X
  - a
  - &a : a
    b: &b
  - &c : &a
  - ? &d
  - ? &e
    : &a
  ```
- Fix [#151](https://github.com/biojppm/rapidyaml/issues/151): scalars beginning with `*` or `&` or `<<` are now correctly quoted when emitting ([PR #156](https://github.com/biojppm/rapidyaml/pull/156)).
- Also from [PR #156](https://github.com/biojppm/rapidyaml/pull/156), map inheritance nodes like `<<: *anchor` or `<<: [*anchor1, *anchor2]` now have a `KEYREF` flag in their type (until a call to `Tree::resolve()`):
  ```c++
  Tree tree = parse("{map: &anchor {foo: bar}, copy: {<<: *anchor}}");
  assert(tree["copy"]["<<"].is_key_ref()); // previously this did not hold
  assert(tree["copy"]["<<"].is_val_ref()); // ... but this did
  ```

#### Tags
- Fix parsing of tag dense maps and seqs ([PR #144](https://github.com/biojppm/rapidyaml/pull/144)):
  ```yaml
  --- !!map {
    k: !!seq [ a, !!str b],
    j: !!seq
       [ a, !!str b]
  --- !!seq [
    !!map { !!str k: v},
    !!map { !!str ? k: v}
  ]
  --- !!map
  !!str foo: !!map  # there was a parse error with the multiple tags
    !!int 1: !!float 20.0
    !!int 3: !!float 40.0
  --- !!seq
  - !!map
    !!str k1: v1
    !!str k2: v2
    !!str k3: v3
  ```

#### Whitespace
- Fix parsing of double-quoted scalars with tabs ([PR #145](https://github.com/biojppm/rapidyaml/pull/145)):
  ```yaml
  "This has a\ttab"
  # is now correctly parsed as "This has a<TAB>tab"
  ```
- Fix filtering of leading and trailing whitespace within double-quoted scalars ([PR #145](https://github.com/biojppm/rapidyaml/pull/145)):
  ```yaml
  # test case 4ZYM, 7A4E, TL85
  "
  <SPC><SPC>foo<SPC>
  <SPC> 
  <SPC><TAB><SPC>bar
  <SPC><SPC>baz
  "
  # is now correctly parsed as " foo\nbar\nbaz "
  ```
- Fix parsing of tabs within YAML tokens ([PR #145](https://github.com/biojppm/rapidyaml/pull/145)):
  ```yaml
  ---<TAB>scalar   # test case K54U
  ---<TAB>{}       # test case Q5MG
  ---              # test case DC7X
  a: b<TAB>
  seq:<TAB>
   - a<TAB>
  c: d<TAB>#X
  ```
- Fix parsing of flow-style maps with ommitted values without any space ([PR #145](https://github.com/biojppm/rapidyaml/pull/145)):
  ```yaml
  # test case 4ABK
  - {foo: , bar: , baz: }  # this was parsed correctly as {foo: ~, bar: ~, baz: ~}
  - {foo:, bar:, baz:}     # ... but this was parsed as {'foo:': , 'bar:': ~, 'baz:': ~}
  ```

#### Scalars
- Unescape forward slashes in double quoted string ([PR #145](https://github.com/biojppm/rapidyaml/pull/145)):
  ```yaml
  --- escaped slash: "a\/b"   # test case 3UYS
  # is now parsed as:
  --- escaped slash: "a/b"
  ```
- Fix filtering of indented regions in folded scalars ([PR #145](https://github.com/biojppm/rapidyaml/pull/145)):
  ```yaml
  # test case 7T8X
  - >
    
    folded
    line
    
    next
    line
      * bullet
    
      * list
      * lines
    
    last
    line
  ```
  is now correctly parsed as `\nfolded line\nnext line\n  * bullet\n\n  * list\n  * lines\n\nlast line\n`.
- Fix parsing of special characters within plain scalars ([PR #145](https://github.com/biojppm/rapidyaml/pull/145)):
  ```yaml
  # test case 3MYT
  k:#foo
    &a !t s
    !t s
  # now correctly parsed as "k:#foo &a !t s !t s"
  ```
- Fix parsing of comments after complex keys ([PR #145](https://github.com/biojppm/rapidyaml/pull/145)):
  ```yaml
  # test case X8DW
  ? key
  # comment 
  : value
  # now correctly parsed as {key: value}
  ```
- Fix parsing of consecutive complex keys within maps ([PR #145](https://github.com/biojppm/rapidyaml/pull/145))
  ```yaml
  # test case 7W2P, ZWK4
  ? a
  ? b
  c:
  ? d
  e:
  # now correctly parsed as {a: ~, b: ~, c: ~, d: ~, e: ~}
  ```
- Fix [#152](https://github.com/biojppm/rapidyaml/issues/152):  parse error with folded scalars that are the last in a container ([PR #157](https://github.com/biojppm/rapidyaml/pull/157)):
  ```yaml
  exec:
    command:
      # before the fix, this folded scalar failed to parse
      - |
        exec pg_isready -U "dog" -d "dbname=dog" -h 127.0.0.1 -p 5432
    parses: no
  ```
- Fix: documents consisting of a quoted scalar now retain the VALQUO flag ([PR #156](https://github.com/biojppm/rapidyaml/pull/156))
  ```c++
  Tree tree = parse("'this is a quoted scalar'");
  assert(tree.rootref().is_doc());
  assert(tree.rootref().is_val());
  assert(tree.rootref().is_val_quoted());
  ```


#### Document structure
- Empty docs are now parsed as a docval with a null node:
  ```yaml
  ---   # test cases 6XDY, 6ZKB, 9BXL, PUW8
  ---
  ---
  ```
  is now parsed as
  ```yaml
  --- ~
  --- ~
  --- ~
  ```
- Prevent creation of DOC nodes from stream-level comments or tags ([PR #145](https://github.com/biojppm/rapidyaml/pull/145)):
  ```yaml
  !foo "bar"
  ...
  # Global
  %TAG ! tag:example.com,2000:app/
  ---
  !foo "bar"
  ```
  was parsed as
  ```yaml
  ---
  !foo "bar"
  ---
  # notice the empty doc in here
  ---
  !foo "bar"
  ```
  and it is now correctly parsed as
  ```yaml
  ---
  !foo "bar"
  ---
  !foo "bar"
  ```
  (other than the known limitation that ryml does not do tag lookup).


#### General

- Fix [#147](https://github.com/biojppm/rapidyaml/issues/147): serialize/deserialize special float values `.nan`, `.inf`, `-.inf` ([PR #149](https://github.com/biojppm/rapidyaml/pull/149))
- Fix [#142](https://github.com/biojppm/rapidyaml/issues/142): `preprocess_json()`: ensure quoted ranges are skipped when slurping containers
- Ensure error macros expand to a single statement ([PR #141](https://github.com/biojppm/rapidyaml/pull/141))
- Update c4core to [0.1.4](https://github.com/biojppm/c4core/releases/tag/v0.1.4)


### Special thanks

- @Gei0r

