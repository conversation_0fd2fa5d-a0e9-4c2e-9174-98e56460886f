<?php
return array(
	0    => 'Novice',
	1    => 'Swordsman',
	2    => 'Mage',
	3    => '<PERSON>',
	4    => 'Acolyte',
	5    => 'Merchant',
	6    => 'Thief',
	7    => 'Knight',
	8    => 'Priest',
	9    => 'Wizard',
	10   => 'Blacksmith',
	11   => '<PERSON>',
	12   => 'Assassin',
	//13   => 'Knight (Mounted)',
	14   => 'Crusader',
	15   => 'Monk',
	16   => 'Sage',
	17   => 'Rogue',
	18   => 'Alchemist',
	19   => 'Bard',
	20   => 'Dancer',
	//21   => 'Crusader (Mounted)',
	22   => 'Wedding',
	23   => 'Super Novice',
	24   => 'Gunslinger',
	25   => 'Ninja',
	26   => 'Xmas',
	27   => 'Summer',
	28   => 'Hanbok',
	29   => 'Oktoberfest',

	4001 => 'High Novice',
	4002 => 'High Swordsman',
	4003 => 'High Mage',
	4004 => 'High Archer',
	4005 => 'High Acolyte',
	4006 => 'High Merchant',
	4007 => 'High Thief',
	4008 => 'Lord Knight',
	4009 => 'High Priest',
	4010 => 'High Wizard',
	4011 => '<PERSON>smith',
	4012 => 'Sniper',
	4013 => 'Assassin Cross',
	//4014 => 'Lord Knight (Mounted)',
	4015 => 'Paladin',
	4016 => 'Champion',
	4017 => 'Professor',
	4018 => 'Stalker',
	4019 => 'Creator',
	4020 => 'Clown',
	4021 => 'Gypsy',
	//4022 => 'Paladin (Mounted)',

	4023 => 'Baby',
	4024 => 'Baby Swordsman',
	4025 => 'Baby Mage',
	4026 => 'Baby Archer',
	4027 => 'Baby Acolyte',
	4028 => 'Baby Merchant',
	4029 => 'Baby Thief',
	4030 => 'Baby Knight',
	4031 => 'Baby Priest',
	4032 => 'Baby Wizard',
	4033 => 'Baby Blacksmith',
	4034 => 'Baby Hunter',
	4035 => 'Baby Assassin',
	//4036 => 'Baby Knight (Mounted)',
	4037 => 'Baby Crusader',
	4038 => 'Baby Monk',
	4039 => 'Baby Sage',
	4040 => 'Baby Rogue',
	4041 => 'Baby Alchemist',
	4042 => 'Baby Bard',
	4043 => 'Baby Dancer',
	//4044 => 'Baby Crusader (Mounted)',
	4045 => 'Super Baby',
    
	4046 => 'Taekwon',
	4047 => 'Star Gladiator',
	//4048 => 'Star Gladiator (Flying)',
	4049 => 'Soul Linker',

	4050 => 'Jiang Shi',
	4051 => 'Death Knight',
	4052 => 'Dark Collector',

	4054 => 'Rune Knight',
	4055 => 'Warlock',
	4056 => 'Ranger',
	4057 => 'Arch Bishop',
	4058 => 'Mechanic',
	4059 => 'Guillotine Cross',
	4060 => 'Rune Knight+',
	4061 => 'Warlock+',
	4062 => 'Ranger+',
	4063 => 'Arch Bishop+',
	4064 => 'Mechanic+',
	4065 => 'Guillotine Cross+',
	4066 => 'Royal Guard',
	4067 => 'Sorcerer',
	4068 => 'Minstrel',
	4069 => 'Wanderer',
	4070 => 'Sura',
	4071 => 'Genetic',
	4072 => 'Shadow Chaser',
	4073 => 'Royal Guard+',
	4074 => 'Sorcerer+',
	4075 => 'Minstrel+',
	4076 => 'Wanderer+',
	4077 => 'Sura+',
	4078 => 'Genetic+',
	4079 => 'Shadow Chaser+',

	//4080 => 'Rune Knight (Mounted)',
	//4081 => 'Rune Knight+ (Mounted)',
	//4082 => 'Royal Guard (Mounted)',
	//4083 => 'Royal Guard+ (Mounted)',
	//4084 => 'Ranger (Mounted)',
	//4085 => 'Ranger+ (Mounted)',
	//4086 => 'Mechanic (Magic Gear)',
	//4087 => 'Mechanic+ (Magic Gear)',

	4096 => 'Baby Rune Knight',
	4097 => 'Baby Warlock',
	4098 => 'Baby Ranger',
	4099 => 'Baby Arch Bishop',
	4100 => 'Baby Mechanic',
	4101 => 'Baby Guillotine Cross',
	4102 => 'Baby Royal Guard',
	4103 => 'Baby Sorcerer',
	4104 => 'Baby Minstrel',
	4105 => 'Baby Wanderer',
	4106 => 'Baby Sura',
	4107 => 'Baby Genetic',
	4108 => 'Baby Shadow Chaser',
	
	//4109 => 'Baby Rune Knight (Mounted)',
	//4110 => 'Baby Royal Guard (Mounted)',
	//4111 => 'Baby Ranger (Mounted)',
	//4112 => 'Baby Mechanic (Magic Gear)',
	
	4190 => 'Expanded Super Novice',
	4191 => 'Expanded Super Baby',
	
	4211 => 'Kagerou',
	4212 => 'Oboro',
	
	4215 => 'Rebellion',
	4218 => 'Summoner',

	4220 => 'Baby Summoner',
	4222 => 'Baby Ninja',
	4223 => 'Baby Kagero',
	4224 => 'Baby Oboro',
	4225 => 'Baby Taekwon',
	4226 => 'Baby Star Gladiator',
	4227 => 'Baby Soul Linker',
	4228 => 'Baby Gunslinger',
	4229 => 'Baby Rebellion',
	//4238 => 'Baby Star Gladiator (Union)',
	
	//4238 => 'Baby Star Glad (Union)',
	4239 => 'Star Emperor',
	4240 => 'Soul Reaper',
	4241 => 'Baby Star Emperor',
	4242 => 'Baby Soul Reaper',

	4252 => 'Dragon Knight',
	4253 => 'Meister',
	4254 => 'Shadow Cross',
	4255 => 'Arch Mage',
	4256 => 'Cardinal',
	4257 => 'WindHawk',
	4258 => 'Imperial Guard',
	4259 => 'Biolo',
	4260 => 'Abyss Chaser',
	4261 => 'Elemental Master',
	4262 => 'Inquisitor',
	4263 => 'Troubadour',
	4264 => 'Trouvere',
)
?>
