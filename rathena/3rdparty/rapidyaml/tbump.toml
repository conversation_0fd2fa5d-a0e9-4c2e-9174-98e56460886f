# https://github.com/TankerHQ/tbump
# https://hackernoon.com/lets-automate-version-number-updates-not-a91q3x7n

# Uncomment this if your project is hosted on GitHub:
github_url = "https://github.com/biojppm/rapidyaml/"

[version]
current = "0.4.0"

# Example of a semver regexp.
# Make sure this matches current_version before
# using tbump
regex = '''
  (?P<major>\d+)
  \.
  (?P<minor>\d+)
  \.
  (?P<patch>\d+)
  (-(?P<release>[a-z]+)(?P<build>\d+))?
  '''

[git]
message_template = "chg: pkg: version {new_version}"
tag_template = "v{new_version}"

# For each file to patch, add a [[file]] config section containing
# the path of the file, relative to the tbump.toml location.
[[file]]
src = "CMakeLists.txt"
search = "c4_project\\(VERSION {current_version}"
[[file]]
src = "test/test_install/CMakeLists.txt"
search = "c4_project\\(VERSION {current_version}"
[[file]]
src = "test/test_singleheader/CMakeLists.txt"
search = "c4_project\\(VERSION {current_version}"

# You can specify a list of commands to
# run after the files have been patched
# and before the git commit is made

#  [[before_commit]]
#  name = "check changelog"
#  cmd = "grep -q {new_version} Changelog.rst"

# TODO: add version header, containing commit hash
# TODO: consolidate changelog from the git logs:
# https://pypi.org/project/gitchangelog/
# https://blogs.sap.com/2018/06/22/generating-release-notes-from-git-commit-messages-using-basic-shell-commands-gitgrep/
# https://medium.com/better-programming/create-your-own-changelog-generator-with-git-aefda291ea93

# Or run some commands after the git tag and the branch
# have been pushed:
#  [[after_push]]
#  name = "publish"
#  cmd = "./publish.sh"
