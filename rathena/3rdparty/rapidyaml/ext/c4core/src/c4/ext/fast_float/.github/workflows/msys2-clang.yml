name: MSYS2-CLANG-CI

on: [push, pull_request]

jobs:
  windows-mingw:
    name: ${{ matrix.msystem }}
    runs-on: windows-latest
    defaults:
      run:
        shell: msys2 {0}
    strategy:
      fail-fast: false
      matrix:
        include:
          - msystem: "MINGW64"
            install: mingw-w64-x86_64-libxml2 mingw-w64-x86_64-cmake mingw-w64-x86_64-ninja mingw-w64-x86_64-clang
            type: Release
          - msystem: "MINGW32"
            install: mingw-w64-i686-libxml2 mingw-w64-i686-cmake mingw-w64-i686-ninja mingw-w64-i686-clang
            type: Release
    env:
      CMAKE_GENERATOR: Ninja

    steps:
      - uses: actions/checkout@v2
      - uses: msys2/setup-msys2@v2
        with:
          update: true
          msystem: ${{ matrix.msystem }}
          install: ${{ matrix.install }}
      - name: Prepare build dir
        run: mkdir build
      - name: Configure
        run: cd build && cmake -DCMAKE_CXX_COMPILER=clang++ -DCMAKE_BUILD_TYPE=${{ matrix.type }} -DFASTFLOAT_TEST=ON ..
      - name: Build
        run: cmake --build build
      - name: Run basic tests
        run: cd build && ctest --output-on-failure -R basictest
