name: VS16-CLANG-CI

on: [push, pull_request]

jobs:
  ci:
    name: windows-vs16
    runs-on: windows-latest
    strategy:
      fail-fast: false
      matrix:
        include:
          - {gen: Visual Studio 16 2019, arch: Win32}
          - {gen: Visual Studio 16 2019, arch: x64}
    steps:
      - name: checkout
        uses: actions/checkout@v2
      - name: Configure
        run: |
          mkdir build
          cd build && cmake -G "${{matrix.gen}}" -A ${{matrix.arch}} -T ClangCL -DFASTFLOAT_TEST=ON ..
      - name: Build
        run: cmake --build build --config Release --parallel
      - name: Run basic tests
        run: |
          cd build
          ctest -C Release --output-on-failure -R basictest
