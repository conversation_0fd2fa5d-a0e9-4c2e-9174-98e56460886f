Multi-line macros support is not guaranteed with gcc-4.8.

This uses temporary objects to work-arround this limitation, main drawback is
that compared code is not displayed in message anymore (only "val" placeholders).

--- googletest/include/gtest/gtest.h
+++ googletest/include/gtest/gtest.h
@@ -2040,6 +2040,80 @@ class TestWithParam : public Test, publi
 //   ASSERT_LT(i, array_size);
 //   ASSERT_GT(records.size(), 0) << "There is no record left.";
 
+#if __GNUC__ == 4 && __GNUC_MINOR__ >= 8
+/*
+ * multi-line macros support is not guaranteed with gcc-4.8.
+ * This uses temporary objects to work-arround this limitation, main drawback is
+ * that compared code is not displayed in message anymore (only "val" placeholders)
+ */
+
+enum class CompatExpectSelector { EQ, NE, LE, LT, GE, GT };
+
+template <CompatExpectSelector T>
+struct CompatExpect
+{
+  const char *file;
+  int line;
+  ::testing::AssertionResult gtest_ar = AssertionSuccess();
+  ::testing::Message msg;
+
+  CompatExpect(const char *file, int line) : file(file), line(line) {}
+  ~CompatExpect() {
+    if (!gtest_ar)
+      GTEST_MESSAGE_AT_(file, line, gtest_ar.failure_message(), ::testing::TestPartResult::kNonFatalFailure) << msg;
+  }
+
+  template <typename T1, typename T2, CompatExpectSelector SEL = T, typename std::enable_if<SEL == CompatExpectSelector::EQ, int>::type = 0>
+  CompatExpect<T> &operator ()(const T1 &val1, const T2 &val2) {
+    gtest_ar = ::testing::internal::EqHelper::Compare("val1", "val2", val1, val2);
+    return *this;
+  }
+
+  template <typename T1, typename T2, CompatExpectSelector SEL = T, typename std::enable_if<SEL == CompatExpectSelector::NE, int>::type = 0>
+  CompatExpect<T> &operator ()(const T1 &val1, const T2 &val2) {
+    gtest_ar = ::testing::internal::CmpHelperNE("val1", "val2", val1, val2);
+    return *this;
+  }
+
+  template <typename T1, typename T2, CompatExpectSelector SEL = T, typename std::enable_if<SEL == CompatExpectSelector::LE, int>::type = 0>
+  CompatExpect<T> &operator ()(const T1 &val1, const T2 &val2) {
+    gtest_ar = ::testing::internal::CmpHelperLE("val1", "val2", val1, val2);
+    return *this;
+  }
+
+  template <typename T1, typename T2, CompatExpectSelector SEL = T, typename std::enable_if<SEL == CompatExpectSelector::LT, int>::type = 0>
+  CompatExpect<T> &operator ()(const T1 &val1, const T2 &val2) {
+    gtest_ar = ::testing::internal::CmpHelperLT("val1", "val2", val1, val2);
+    return *this;
+  }
+
+  template <typename T1, typename T2, CompatExpectSelector SEL = T, typename std::enable_if<SEL == CompatExpectSelector::GE, int>::type = 0>
+  CompatExpect<T> &operator ()(const T1 &val1, const T2 &val2) {
+    gtest_ar = ::testing::internal::CmpHelperGE("val1", "val2", val1, val2);
+    return *this;
+  }
+
+  template <typename T1, typename T2, CompatExpectSelector SEL = T, typename std::enable_if<SEL == CompatExpectSelector::GT, int>::type = 0>
+  CompatExpect<T> &operator ()(const T1 &val1, const T2 &val2) {
+    gtest_ar = ::testing::internal::CmpHelperGT("val1", "val2", val1, val2);
+    return *this;
+  }
+
+  template <typename T1>
+  CompatExpect &operator << (const T1 &t) {
+    msg << t;
+    return *this;
+  }
+};
+#define EXPECT_EQ ::testing::CompatExpect<::testing::CompatExpectSelector::EQ>{__FILE__,__LINE__}
+#define EXPECT_NE ::testing::CompatExpect<::testing::CompatExpectSelector::NE>{__FILE__,__LINE__}
+#define EXPECT_LE ::testing::CompatExpect<::testing::CompatExpectSelector::LE>{__FILE__,__LINE__}
+#define EXPECT_LT ::testing::CompatExpect<::testing::CompatExpectSelector::LT>{__FILE__,__LINE__}
+#define EXPECT_GE ::testing::CompatExpect<::testing::CompatExpectSelector::GE>{__FILE__,__LINE__}
+#define EXPECT_GT ::testing::CompatExpect<::testing::CompatExpectSelector::GT>{__FILE__,__LINE__}
+
+#else
+
 #define EXPECT_EQ(val1, val2) \
   EXPECT_PRED_FORMAT2(::testing::internal::EqHelper::Compare, val1, val2)
 #define EXPECT_NE(val1, val2) \
@@ -2053,6 +2127,8 @@ class TestWithParam : public Test, publi
 #define EXPECT_GT(val1, val2) \
   EXPECT_PRED_FORMAT2(::testing::internal::CmpHelperGT, val1, val2)
 
+#endif
+
 #define GTEST_ASSERT_EQ(val1, val2) \
   ASSERT_PRED_FORMAT2(::testing::internal::EqHelper::Compare, val1, val2)
 #define GTEST_ASSERT_NE(val1, val2) \
