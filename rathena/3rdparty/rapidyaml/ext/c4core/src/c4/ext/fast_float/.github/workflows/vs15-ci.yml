name: VS15-CI

on: [push, pull_request]

jobs:
  ci:
    if: >-
      ! contains(toJSON(github.event.commits.*.message), '[skip ci]') &&
      ! contains(toJSON(github.event.commits.*.message), '[skip github]')
    name: windows-vs15
    runs-on: windows-2016
    strategy:
      fail-fast: false
      matrix:
        include:
          - {gen: Visual Studio 15 2017, arch: Win32}
          - {gen: Visual Studio 15 2017, arch: x64}
    steps:
    - uses: actions/checkout@v2
    - name: Configure
      run: |
          mkdir build
          cd build && cmake  -G "${{matrix.gen}}" -A ${{matrix.arch}} -DFASTFLOAT_TEST=ON ..
    - name: Build
      run: cmake --build build --verbose --config Release --parallel
    - name: 'Run CTest'
      run: |
          cd build
          ctest -C Release --output-on-failure -R basictest