services:
  # MariaDB Database Service
  mariadb:
    image: mariadb:10.11
    container_name: ragnarok_mariadb
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ragnarok_root_password
      MYSQL_DATABASE: ragnarok
      MYSQL_USER: ragnarok
      MYSQL_PASSWORD: ragnarok_password
    volumes:
      - mariadb_data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d
      - ./config/mariadb/my.cnf:/etc/mysql/conf.d/custom.cnf:ro
    ports:
      - "3306:3306"
    networks:
      - ragnarok_network
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci

  # rAthena Server Service
  rathena:
    build:
      context: ./rathena
      dockerfile: Dockerfile
    container_name: ragnarok_rathena
    restart: unless-stopped
    depends_on:
      - mariadb
    environment:
      DB_HOST: mariadb
      DB_PORT: 3306
      DB_NAME: ragnarok
      DB_USER: ragnarok
      DB_PASSWORD: ragnarok_password
    volumes:
      - ./rathena:/rathena
      - ./config/rathena:/rathena/conf
      - ./logs/rathena:/rathena/log
      - rathena_data:/rathena/save
    ports:
      - "6900:6900"  # Login server
      - "6121:6121"  # Character server
      - "5121:5121"  # Map server
    networks:
      - ragnarok_network
    stdin_open: true
    tty: true

  # FluxCP Web Control Panel
  fluxcp:
    build:
      context: ./fluxcp
      dockerfile: Dockerfile
    container_name: ragnarok_fluxcp
    restart: unless-stopped
    depends_on:
      - mariadb
      - rathena
    environment:
      DB_HOST: mariadb
      DB_PORT: 3306
      DB_NAME: ragnarok
      DB_USER: ragnarok
      DB_PASSWORD: ragnarok_password
      RATHENA_HOST: rathena
    volumes:
      - ./fluxcp:/var/www/html
      - ./logs/fluxcp:/var/log/apache2
    ports:
      - "8080:80"
    networks:
      - ragnarok_network

  # phpMyAdmin Database Administration
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:5.2
    container_name: ragnarok_phpmyadmin
    restart: unless-stopped
    depends_on:
      - mariadb
    environment:
      PMA_HOST: mariadb
      PMA_PORT: 3306
      PMA_USER: ragnarok
      PMA_PASSWORD: ragnarok_password
      MYSQL_ROOT_PASSWORD: ragnarok_root_password
      UPLOAD_LIMIT: 100M
    ports:
      - "8081:80"
    networks:
      - ragnarok_network

networks:
  ragnarok_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  mariadb_data:
    driver: local
  rathena_data:
    driver: local
