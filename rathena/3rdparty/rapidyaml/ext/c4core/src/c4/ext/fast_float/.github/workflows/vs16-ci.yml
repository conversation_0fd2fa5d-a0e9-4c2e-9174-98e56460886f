name: VS16-CI

on: [push, pull_request]

jobs:
  ci:
    name: windows-vs16
    runs-on: windows-latest
    strategy:
      fail-fast: false
      matrix:
        include:
          - {gen: Visual Studio 16 2019, arch: Win32}
          - {gen: Visual Studio 16 2019, arch: x64}
    steps:
      - name: checkout
        uses: actions/checkout@v2
      - name: Use cmake
        run: |
          mkdir build &&
          cd build &&
          cmake  ${{matrix.cxx}} ${{matrix.arch}} -DFASTFLOAT_TEST=ON -DCMAKE_INSTALL_PREFIX:PATH=destination ..  &&
          cmake --build .  --verbose &&
          ctest --output-on-failure  &&
          cmake --install . &&
          cd ../tests/installation_tests/find  &&
          mkdir build && cd build && cmake -DCMAKE_INSTALL_PREFIX:PATH=../../../build/destination .. &&  cmake --build .   --verbose
          cd ../../issue72_installation  &&
          mkdir build && cd build && cmake -DCMAKE_INSTALL_PREFIX:PATH=../../../build/destination .. &&  cmake --build .    --verbose