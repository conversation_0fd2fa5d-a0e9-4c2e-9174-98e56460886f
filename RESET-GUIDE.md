# RagnaDocker Factory Reset Guide

## 🔄 Overview

The RagnaDocker project includes a comprehensive "factory reset" capability that returns your development environment to its initial clean state. This is useful for:

- **Starting fresh** after experimenting with configurations
- **Troubleshooting** persistent issues by eliminating all cached data
- **Preparing for redistribution** by removing all downloaded dependencies
- **Testing the setup process** from a clean slate
- **Freeing up disk space** by removing all Docker artifacts and source code

## ⚠️ **CRITICAL WARNING**

**This reset operation is IRREVERSIBLE and will delete ALL data!**

### What Gets Deleted:
- ❌ All rAthena source code and compiled binaries
- ❌ All FluxCP source code and user data
- ❌ All database data (characters, accounts, guilds, etc.)
- ❌ All Docker images, containers, and volumes
- ❌ All log files and temporary data
- ❌ All database backups (except the init script)

### What Gets Preserved:
- ✅ Project structure and directories
- ✅ docker-compose.yml configuration
- ✅ Custom Dockerfiles and configurations
- ✅ Makefile and documentation
- ✅ Custom scripts and tools
- ✅ Git repository (if applicable)

## 🛠️ Reset Methods

### Method 1: Using the Reset Script (Recommended)

```bash
./reset-project.sh
```

**Features:**
- Interactive confirmation prompts
- Detailed progress reporting
- Colored output for better visibility
- Comprehensive cleanup of all components
- Safety checks and validation

### Method 2: Using Makefile Target

```bash
make reset
```

**Features:**
- Integrated with project workflow
- Quick access via make command
- Built-in safety confirmation
- Calls the reset script internally

## 🏗️ Architectural Design

### Reset Process Flow

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Validation    │───▶│  Confirmation   │───▶│   Execution     │
│                 │    │                 │    │                 │
│ • Check location│    │ • User prompt   │    │ • Stop services │
│ • Verify files  │    │ • Double confirm│    │ • Clean Docker  │
│ • Safety checks │    │ • Type "RESET"  │    │ • Remove files  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Completion    │◀───│   Restoration   │◀───│    Cleanup      │
│                 │    │                 │    │                 │
│ • Success msg   │    │ • Restore custom│    │ • Logs cleanup  │
│ • Next steps    │    │ • Preserve core │    │ • SQL cleanup   │
│ • Instructions  │    │ • Directory fix │    │ • System prune  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Component Breakdown

#### 1. **Docker Cleanup Engine**
```bash
# Stop and remove containers
docker-compose down --remove-orphans

# Remove project images
docker rmi $(docker images --filter "reference=ragnadocker*" -q) --force

# Remove project volumes
docker volume rm $(docker volume ls --filter "name=ragnadocker" -q) --force

# Remove project networks
docker network rm $(docker network ls --filter "name=ragnadocker" -q)
```

#### 2. **Source Code Cleanup Engine**
```bash
# rAthena cleanup (preserve custom files)
mkdir -p /tmp/rathena_backup
cp rathena/Dockerfile /tmp/rathena_backup/
cp rathena/start.sh /tmp/rathena_backup/
rm -rf rathena/*
cp /tmp/rathena_backup/* rathena/

# FluxCP cleanup (preserve custom files)
mkdir -p /tmp/fluxcp_backup
cp fluxcp/Dockerfile* /tmp/fluxcp_backup/
cp fluxcp/*.conf /tmp/fluxcp_backup/
cp fluxcp/*.ini /tmp/fluxcp_backup/
rm -rf fluxcp/*
cp /tmp/fluxcp_backup/* fluxcp/
```

#### 3. **Data Cleanup Engine**
```bash
# Clean logs
rm -rf logs/*

# Clean SQL backups (preserve init script)
find sql/ -name "*.sql" -not -name "01-init-database.sql" -delete

# Docker system cleanup
docker system prune -f --volumes
```

## 🔒 Safety Considerations

### Multi-Level Confirmation System

1. **Initial Warning**: Displays comprehensive list of what will be deleted
2. **First Confirmation**: User must type "yes" exactly
3. **Final Confirmation**: User must type "RESET" in uppercase
4. **Location Validation**: Ensures script runs from correct directory

### File Preservation Logic

The reset script uses a sophisticated backup-and-restore mechanism:

```bash
# Backup phase
mkdir -p /tmp/component_backup
cp important_files /tmp/component_backup/

# Destruction phase
rm -rf component/*

# Restoration phase
cp /tmp/component_backup/* component/
rm -rf /tmp/component_backup
```

### Error Handling

- **Set -e**: Script exits immediately on any error
- **Validation checks**: Verifies environment before proceeding
- **Graceful failures**: Continues cleanup even if some steps fail
- **Status reporting**: Clear success/failure messages for each step

## 🚀 Rebuilding After Reset

### Automatic Rebuild (Recommended)

```bash
./start.sh
```

This will:
1. Download rAthena source code
2. Download FluxCP source code
3. Build all Docker images
4. Start all services
5. Initialize the database
6. Display access URLs

### Manual Rebuild

```bash
# Step 1: Build and start services
make setup

# Step 2: Import database schema
make setup-db

# Step 3: Check status
make status
```

### Verification Steps

After rebuilding, verify everything works:

```bash
# Check service status
docker-compose ps

# Test database connection
docker-compose exec mariadb mysql -u ragnarok -pragnarok_password -e "SELECT 1"

# Test FluxCP
curl -I http://localhost:8080

# Test phpMyAdmin
curl -I http://localhost:8081
```

## 📊 Disk Space Recovery

The reset operation typically recovers:

- **Docker Images**: 2-4 GB
- **Docker Volumes**: 100-500 MB
- **Source Code**: 200-400 MB
- **Log Files**: 10-100 MB
- **Total Recovery**: ~3-5 GB

## 🔧 Customization

### Adding Custom Preservation Rules

Edit `reset-project.sh` to preserve additional files:

```bash
# Add to backup phase
[ -f "custom/important.conf" ] && cp "custom/important.conf" /tmp/backup/

# Add to restoration phase
[ -f "/tmp/backup/important.conf" ] && cp /tmp/backup/important.conf custom/
```

### Selective Reset Options

You can create partial reset functions:

```bash
# Reset only Docker artifacts
make clean

# Reset only source code (manual)
rm -rf rathena/src rathena/db rathena/npc
rm -rf fluxcp/lib fluxcp/modules fluxcp/themes

# Reset only database
docker-compose down
docker volume rm ragnadocker_mariadb_data
```

## 🆘 Emergency Recovery

If the reset script fails midway:

1. **Manual Docker cleanup**:
   ```bash
   docker-compose down --remove-orphans
   docker system prune -a --volumes
   ```

2. **Manual file cleanup**:
   ```bash
   rm -rf rathena/* fluxcp/* logs/*
   git checkout rathena/Dockerfile fluxcp/Dockerfile
   ```

3. **Rebuild from scratch**:
   ```bash
   ./start.sh
   ```

## 📝 Best Practices

1. **Always backup important data** before running reset
2. **Test the reset process** in a development environment first
3. **Document any custom modifications** you want to preserve
4. **Use version control** to track your configuration changes
5. **Run reset during maintenance windows** to avoid disruption

---

**Remember**: The factory reset is a powerful tool for maintaining a clean development environment, but it should be used with caution and proper planning! 🛡️
