name: VS16-<PERSON><PERSON> C++20

on: [push, pull_request]

jobs:
  ci:
    name: windows-vs16
    runs-on: windows-latest
    strategy:
      fail-fast: false
      matrix:
        include:
          - {gen: Visual Studio 16 2019, arch: Win32}
          - {gen: Visual Studio 16 2019, arch: x64}
    steps:
      - name: checkout
        uses: actions/checkout@v2
      - name: Use cmake
        run: |
          mkdir build &&
          cd build &&
          cmake  ${{matrix.cxx}} ${{matrix.arch}}  -DCMAKE_CXX_STANDARD=20 -DFASTFLOAT_TEST=ON -DCMAKE_INSTALL_PREFIX:PATH=destination ..  &&
          cmake --build .  --verbose &&
          ctest --output-on-failure
