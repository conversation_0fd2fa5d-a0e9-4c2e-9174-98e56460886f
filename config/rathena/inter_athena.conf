// rAthena Inter-server Configuration

// MySQL Login server connection
login_server_ip: mariadb
login_server_port: 3306
login_server_id: ragnarok
login_server_pw: ragnarok_password
login_server_db: ragnarok

// MySQL Character server connection  
char_server_ip: mariadb
char_server_port: 3306
char_server_id: ragnarok
char_server_pw: ragnarok_password
char_server_db: ragnarok

// MySQL Map server connection
map_server_ip: mariadb
map_server_port: 3306
map_server_id: ragnarok
map_server_pw: ragnarok_password
map_server_db: ragnarok

// MySQL Log Database connection
log_db_ip: mariadb
log_db_port: 3306
log_db_id: ragnarok
log_db_pw: ragnarok_password
log_db_db: ragnarok

// Use SQL item_db, mob_db and mob_skill_db for the map server
use_sql_db: yes

// Default codepage
default_codepage: utf8

// Log Inter Connections
log_inter: 1

// Inter Log Filename
inter_log_filename: log/inter.log

// Level range for sharing within a party
party_share_level: 15

// You can specify the codepage to use in your MySQL tables here.
// (Note that this feature requires MySQL 4.1+)
//default_codepage: 

// For IPs, ideally under linux, you want to use localhost instead of 127.0.0.1.
// Under windows, you want to use 127.0.0.1.  If you see a message like
// "Can't connect to local MySQL server through socket '/tmp/mysql.sock' (2)"
// and you have localhost, switch it to 127.0.0.1

// Global account engine configuration
// Database Table configuration
login_db: login
login_db_account_id: account_id
login_db_userid: userid
login_db_user_pass: user_pass
login_db_level: level

// Client MD5 hash check
// If turned on, the login server will run a check of the client MD5 hash
// for the client version specified in the clientinfo file.
// If you want to use this, you need to compile with -DCHECK_CMDLINE
// Note: see doc/md5_hashcheck.txt for more details.
check_client_version: no
