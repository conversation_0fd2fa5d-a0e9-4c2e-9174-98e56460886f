<?php
return array(
	'flag_buyingstore'	=> 'Item is available to Buying Stores',
	'flag_deadbranch'	=> 'Item is a Dead Branch type',
	'flag_container'	=> 'Item is part of a Container',
	'flag_uniqueid'		=> 'Item is a unique stack',
	'flag_bindonequip'	=> 'Item is bound to the character upon equipping',
	'flag_dropannounce'	=> 'Item has a special announcement to self on drop',
	'flag_noconsume'	=> 'Item is consumed on use',
)
?>
