<?php
return array(
	1	=>	'MaxHP +%s',
	2	=>	'MaxSP +%s',
	3	=>	'STR +%s',
	4	=>	'AGI +%s',
	5	=>	'VIT +%s',
	6	=>	'INT +%s',
	7	=>	'DEX +%s',
	8	=>	'LUK +%s',
	9	=>	'MaxHP +%s%%',
	10	=>	'MaxSP +%s%%',
	11	=>	'HP regen +%s%%',
	12	=>	'SP regen +%s%%',
	13	=>	'ATK +%s%%',
	14	=>	'MATK +%s%%',
	15	=>	'ASPD +%s',
	16	=>	'Delay after attack -%s%%',
	17	=>	'ATK +%s',
	18	=>	'HIT +%s',
	19	=>	'MATK +%s',
	20	=>	'DEF +%s',
	21	=>	'MDEF +%s',
	22	=>	'FLEE +%s',
	23	=>	'Perfect dodge +%s',
	24	=>	'CRIT +%s',
	25	=>	'Neutral elemental resistance +%s%%',
	26	=>	'Water elemental resistance +%s%%',
	27	=>	'Earth elemental resistance +%s%%',
	28	=>	'Fire elemental resistance +%s%%',
	29	=>	'Wind elemental resistance +%s%%',
	30	=>	'Poison elemental resistance +%s%%',
	31	=>	'Holy elemental resistance +%s%%',
	32	=>	'Shadow elemental resistance +%s%%',
	33	=>	'Ghost elemental resistance +%s%%',
	34	=>	'Undead elemental resistance +%s%%',
	35	=>	'All elementals resistance +%s%%',
	36	=>	'Neutral monster resistance +%s%%',
	37	=>	'ATK +%s%% against Neutral monster',
	38	=>	'Water monster resistance +%s%%',
	39	=>	'ATK +%s%% against Water monster',
	40	=>	'Earth monster resistance +%s%%',
	41	=>	'ATK +%s%% against Earth monster',
	42	=>	'Fire monster resistance +%s%%',
	43	=>	'ATK +%s%% against Fire monster',
	44	=>	'Wind monster resistance +%s%%',
	45	=>	'ATK +%s%% against Wind monster',
	46	=>	'Poison monster resistance +%s%%',
	47	=>	'ATK +%s%% against Poison monster',
	48	=>	'Holy monster resistance +%s%%',
	49	=>	'ATK +%s%% against Holy monster',
	50	=>	'Shadow monster resistance +%s%%',
	51	=>	'ATK +%s%% against Shadow monster',
	52	=>	'Ghost monster resistance +%s%%',
	53	=>	'ATK +%s%% against Ghost monster',
	54	=>	'Undead monster resistance +%s%%',
	55	=>	'ATK +%s%% against Undead monster',
	56	=>	'Neutral monster magic resistance +%s%%',
	57	=>	'MATK +%s%% against Neutral monster',
	58	=>	'Water monster magic resistance +%s%%',
	59	=>	'MATK +%s%% against Water monster',
	60	=>	'Earth monster magic resistance +%s%%',
	61	=>	'MATK +%s%% against  Earth monster',
	62	=>	'Fire monster magic resistance +%s%%',
	63	=>	'MATK +%s%% against Fire monster',
	64	=>	'Wind monster magic resistance +%s%%',
	65	=>	'MATK +%s%% against Wind monster',
	66	=>	'Poison monster magic resistance +%s%%',
	67	=>	'MATK +%s%% against Poison monster',
	68	=>	'Holy monster magic resistance +%s%%',
	69	=>	'MATK +%s%% against Holy monster',
	70	=>	'Shadow monster magic resistance +%s%%',
	71	=>	'MATK +%s%% against Shadow monster',
	72	=>	'Ghost monster magic resistance +%s%%',
	73	=>	'MATK +%s%% against Ghost monster',
	74	=>	'Undead monster magic resistance +%s%%',
	75	=>	'MATK +%s%% against Undead monster',
	76	=>	'Armor element: Neutral',
	77	=>	'Armor element: Water',
	78	=>	'Armor element: Earth',
	79	=>	'Armor element: Fire',
	80	=>	'Armor element: Wind',
	81	=>	'Armor element: Poison',
	82	=>	'Armor element: Holy',
	83	=>	'Armor element: Shadow',
	84	=>	'Armor element: Ghost',
	85	=>	'Armor element: Undead',
	//86	=>	'',
	87	=>	'Formless monster resistance +%s%%',
	88	=>	'Undead monster resistance +%s%%',
	89	=>	'Brute monster resistance +%s%%',
	90	=>	'Plant monster resistance +%s%%',
	91	=>	'Insect monster resistance +%s%%',
	92	=>	'Fish monster resistance +%s%%',
	93	=>	'Demon monster resistance +%s%%',
	94	=>	'Demihuman monster resistance +%s%%',
	95	=>	'Angel monster resistance +%s%%',
	96	=>	'Dragon monster resistance +%s%%',
	97	=>	'ATK +%s%% against Formless monster',
	98	=>	'ATK +%s%% against Undead monster',
	99	=>	'ATK +%s%% against Brute monster',
	100	=>	'ATK +%s%% against Plant monster',
	101	=>	'ATK +%s%% against Insect monster',
	102	=>	'ATK +%s%% against Fish monster',
	103	=>	'ATK +%s%% against Demon monster',
	104	=>	'ATK +%s%% against Demihuman monster',
	105	=>	'ATK +%s%% against Angel monster',
	106	=>	'ATK +%s%% against Dragon monster',
	107	=>	'MATK +%s%% against Formless monster',
	108	=>	'MATK +%s%% against Undead monster',
	109	=>	'MATK +%s%% against Brute monster',
	110	=>	'MATK +%s%% against Plant monster',
	111	=>	'MATK +%s%% against Insect monster',
	112	=>	'MATK +%s%% against Fish monster',
	113	=>	'MATK +%s%% against Devil monster',
	114	=>	'MATK +%s%% against Demihuman monster',
	115	=>	'MATK +%s%% against Angel monster',
	116	=>	'MATK +%s%% against Dragon monster',
	117	=>	'CRIT +%s against Formless monster',
	118	=>	'CRIT +%s against Undead monster',
	119	=>	'CRIT +%s against Brute monster',
	120	=>	'CRIT +%s against Plant monster',
	121	=>	'CRIT +%s against Insect monster',
	122	=>	'CRIT +%s against Fish monster',
	123	=>	'CRIT +%s against Demon monster',
	124	=>	'CRIT +%s against Demihuman monster',
	125	=>	'CRIT +%s against Angel monster',
	126	=>	'CRIT +%s against Dragon monster',
	127	=>	'Pierces %s%% DEF of Formless monster',
	128	=>	'Pierces %s%% DEF of Undead monster',
	129	=>	'Pierces %s%% DEF of Brute monster',
	130	=>	'Pierces %s%% DEF of Plant monster',
	131	=>	'Pierces %s%% DEF of Insect monster',
	132	=>	'Pierces %s%% DEF of Fish monster',
	133	=>	'Pierces %s%% DEF of Demon monster',
	134	=>	'Pierces %s%% DEF of Demihuman monster',
	135	=>	'Pierces %s%% DEF of Angel monster',
	136	=>	'Pierces %s%% DEF of Dragon monster',
	137	=>	'Pierces %s%% MDEF of Formless monster',
	138	=>	'Pierces %s%% MDEF of Undead monster',
	139	=>	'Pierces %s%% MDEF of Brute monster',
	140	=>	'Pierces %s%% MDEF of Plant monster',
	141	=>	'Pierces %s%% MDEF of Insect monster',
	142	=>	'Pierces %s%% MDEF of Fish monster',
	143	=>	'Pierces %s%% MDEF of Demon monster',
	144	=>	'Pierces %s%% MDEF of Demihuman monster',
	145	=>	'Pierces %s%% MDEF of Angel monster',
	146	=>	'Pierces %s%% MDEF of Dragon monster',
	147	=>	'ATK +%s%% against Normal monster',
	148	=>	'ATK +%s%% against Boss monster',
	149	=>	'Normal monster resistance +%s%%',
	150	=>	'Boss monster resistance +%s%%',
	151	=>	'MATK +%s%% against Normal monster',
	152	=>	'MATK +%s%% against Boss monster',
	153	=>	'Pierces %s%% DEF of Normal monster',
	154	=>	'Pierces %s%% DEF of Boss monster',
	155	=>	'Pierces %s%% MDEF of Normal monster',
	156	=>	'Pierces %s%% MDEF of Boss monster',
	157	=>	'ATK +%s%% against Small size monster',
	158	=>	'ATK +%s%% against Medium size monster',
	159	=>	'ATK +%s%% against Large size monster',
	160	=>	'Small monster resistance +%s%%',
	161	=>	'Medium monster resistance +%s%%',
	162	=>	'Large monster resistance +%s%%',
	163	=>	'Nullify weapon\'s damage size penalty',
	164	=>	'Critical attack +%s%%',
	165	=>	'Critical damage -%s%%',
	166	=>	'Long range physical attack +%s%%',
	167	=>	'Long range physical damage -%s%%',
	168	=>	'Healing skills +%s%%',
	169	=>	'Restoration gained from Healing skills +%s%%',
	170	=>	'Variable cast time -%s%%',
	171	=>	'After cast delay -%s%%',
	172	=>	'Reduces SP cost by %s%%',
	//173	=>	'',
	//174	=>	'',
	175	=>	'Weapon element: Neutral',
	176	=>	'Weapon element: Water',
	177	=>	'Weapon element: Earth',
	178	=>	'Weapon element: Fire',
	179	=>	'Weapon element: Wind',
	180	=>	'Weapon element: Poison',
	181	=>	'Weapon element: Holy',
	182	=>	'Weapon element: Shadow',
	183	=>	'Weapon element: Ghost',
	184	=>	'Weapon element: Undead',
	185	=>	'Indestructible in battle',
	186	=>	'Indestructible in battle',
	187	=>	'MATK against Small size monster +%s%%',
	188	=>	'MATK against Medium size monster +%s%%',
	189	=>	'MATK against Large size monster +%s%%',
	190	=>	'Small monster magic resistance +%s%%',
	191	=>	'Medium monster magic resistance +%s%%',
	192	=>	'Large monster magic resistance +%s%%',
	193	=>	'Elemental attacks resistance +%s%%',
	194	=>	'Formless monster resistance +%s%%',
	195	=>	'Undead monster resistance +%s%%',
	196	=>	'Brute monster resistance +%s%%',
	197	=>	'Plant monster resistance +%s%%',
	198	=>	'Insect monster resistance +%s%%',
	199	=>	'Fish monster resistance +%s%%',
	200	=>	'Demon monster resistance +%s%%',
	201	=>	'Demihuman monster resistance +%s%%',
	202	=>	'Angel monster resistance +%s%%',
	203	=>	'Dragon monster resistance +%s%%',
	204	=>	'Long range physical attack +%s%%',
	205	=>	'Long range physical damage -%s%%',
	206	=>	'Demi-Human players resistance + %s%%',
	207	=>	'Doram players resistance +%s%%',
	208	=>	'ATK against Demi-Human players +%s%%',
	209	=>	'ATK against Doram players +%s%%',
	210	=>	'MATK against Demi-Human players +%s%%',
	211	=>	'MATK against Doram players +%s%%',
	212	=>	'Critical +%s for Demi-Human players',
	213	=>	'Critical +%s for Doram players',
	214	=>	'Pierces %s%% DEF of Demi-Human players',
	215	=>	'Pierces %s%% DEF of Doram players',
	216	=>	'Pierces %s%% MDEF of Demi-Human players',
	217	=>	'Pierces %s%% MDEF of Doram players',
	218	=>	'Recieved reflected damage -%s%%',
	219	=>	'Melee physical damage +%s%%',
	220	=>	'Melee physical damage -%s%%',
	)
?>
