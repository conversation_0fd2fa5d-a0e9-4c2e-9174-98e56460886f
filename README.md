# RagnaDocker - Ragnarok Online Development Environment

A complete Docker Compose environment for developing a Ragnarok Online server based on rAthena, similar to the setup at [RagnaDocker](https://github.com/tv0ll/RagnaDocker).

## 🚀 Features

- **rAthena Server**: Ragnarok Online server with packet version ********
- **MariaDB Database**: Persistent database for character data, accounts, guilds, etc.
- **FluxCP**: Web-based control panel for server administration
- **phpMyAdmin**: Web interface for database management
- **Development-friendly**: Hot reload, volume mounts, and easy rebuilding

## 📋 Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- At least 4GB RAM available for Docker
- Ports 3306, 5121, 6121, 6900, 8080, 8081 available

## 🛠️ Quick Start

### 1. Clone and Setup

```bash
git clone <your-repo-url> ragnadocker
cd ragnadocker
```

### 2. Initial Setup

The first time you run the environment, you need to set up the rAthena source code and database:

```bash
# Start the database first
docker-compose up -d mariadb

# Wait for database to be ready (about 30 seconds)
docker-compose logs -f mariadb

# Once you see "ready for connections", proceed with full startup
docker-compose up -d
```

### 3. Import rAthena Database Schema

After the containers are running, import the rAthena database schema:

```bash
# Download and import the latest rAthena SQL files
docker-compose exec rathena bash -c "
    cd /rathena/sql-files &&
    mysql -h mariadb -u ragnarok -pragnarok_password ragnarok < main.sql &&
    mysql -h mariadb -u ragnarok -pragnarok_password ragnarok < logs.sql &&
    mysql -h mariadb -u ragnarok -pragnarok_password ragnarok < item_db.sql &&
    mysql -h mariadb -u ragnarok -pragnarok_password ragnarok < mob_db.sql
"
```

### 4. Access the Services

- **rAthena Server**:
  - Login Server: `localhost:6900`
  - Character Server: `localhost:6121`
  - Map Server: `localhost:5121`
- **FluxCP**: http://localhost:8080
  - **Installation**: http://localhost:8080/?module=install
- **phpMyAdmin**: http://localhost:8081
- **MariaDB**: `localhost:3306`

## 🔧 Configuration

### Database Credentials

- **Host**: `mariadb` (internal) / `localhost:3306` (external)
- **Database**: `ragnarok`
- **Username**: `ragnarok`
- **Password**: `ragnarok_password`
- **Root Password**: `ragnarok_root_password`

### rAthena Configuration

The rAthena server is configured with:
- Packet version: ********
- Database connection to MariaDB service
- Custom configuration files in `config/rathena/`

### FluxCP Setup

1. Access FluxCP at http://localhost:8080
2. Follow the installation wizard
3. Use the database credentials above
4. Configure the rAthena server connection

## 🔄 Development Workflow

### Making Changes to rAthena

1. **Source Code**: Place your rAthena source in the `rathena/` directory
2. **Rebuild**: `docker-compose build rathena`
3. **Restart**: `docker-compose restart rathena`

### Making Changes to FluxCP

1. **Source Code**: Place your FluxCP source in the `fluxcp/` directory
2. **No rebuild needed**: Changes are reflected immediately due to volume mounts

### Database Management

- Use phpMyAdmin at http://localhost:8081 for GUI management
- Or connect directly: `docker-compose exec mariadb mysql -u ragnarok -p ragnarok`

## 📝 Common Commands

```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f [service_name]

# Stop all services
docker-compose down

# Rebuild a specific service
docker-compose build [service_name]

# Access a container shell
docker-compose exec [service_name] bash

# Factory reset (WARNING: Deletes ALL data and source code)
make reset
# OR
./reset-project.sh

# Quick status check
make status
```

## 🐛 Troubleshooting

### rAthena Server Won't Start

1. Check if the database is ready:
   ```bash
   docker-compose logs mariadb
   ```

2. Verify database connection:
   ```bash
   docker-compose exec rathena mysql -h mariadb -u ragnarok -p
   ```

3. Check rAthena logs:
   ```bash
   docker-compose logs rathena
   ```

### FluxCP Installation Issues

1. Ensure database is accessible
2. Check file permissions in the `fluxcp/` directory
3. Verify Apache configuration in container

### Database Connection Issues

1. Ensure MariaDB container is running:
   ```bash
   docker-compose ps mariadb
   ```

2. Check network connectivity:
   ```bash
   docker-compose exec rathena ping mariadb
   ```

## 🔄 Factory Reset

Need to start fresh? The project includes a comprehensive factory reset capability:

```bash
# Interactive factory reset
./reset-project.sh

# Or via Makefile
make reset
```

**⚠️ WARNING**: This will delete ALL downloaded source code, database data, and Docker artifacts while preserving your project structure and custom configurations.

For detailed information, see:
- `RESET-GUIDE.md` - Comprehensive reset documentation
- `RESET-QUICK-REFERENCE.md` - Quick reference guide

## 📁 Directory Structure

```
ragnadocker/
├── docker-compose.yml          # Main Docker Compose configuration
├── README.md                   # This file
├── Makefile                    # Convenient management commands
├── start.sh                    # Quick start script
├── reset-project.sh            # Factory reset script
├── RESET-GUIDE.md              # Reset documentation
├── RESET-QUICK-REFERENCE.md    # Reset quick reference
├── .env.example                # Environment configuration template
├── .gitignore                  # Git ignore rules
├── rathena/                    # rAthena server files
│   ├── Dockerfile             # rAthena container build
│   └── start.sh               # Server startup script
├── fluxcp/                     # FluxCP web panel files
│   ├── Dockerfile             # FluxCP container build
│   ├── apache-config.conf     # Apache configuration
│   └── php.ini                # PHP configuration
├── config/                     # Configuration files
│   ├── mariadb/
│   │   └── my.cnf             # MariaDB configuration
│   ├── rathena/               # rAthena configuration files
│   └── fluxcp/                # FluxCP configuration files
├── sql/                        # Database initialization scripts
├── logs/                       # Log files
│   ├── rathena/               # rAthena server logs
│   └── fluxcp/                # FluxCP/Apache logs
└── volumes/                    # Docker volumes (auto-created)
```

## 🔒 Security Notes

- **Change default passwords** before deploying to production
- **Firewall configuration** should restrict access to necessary ports only
- **Regular backups** of the database are recommended
- **Update dependencies** regularly for security patches

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- [rAthena Team](https://github.com/rathena/rathena) for the excellent Ragnarok Online server emulator
- [FluxCP Team](https://github.com/FluxCP/FluxCP) for the web control panel
- [tv0ll/RagnaDocker](https://github.com/tv0ll/RagnaDocker) for inspiration
