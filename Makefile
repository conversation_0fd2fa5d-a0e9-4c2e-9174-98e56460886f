# RagnaDocker Makefile
# Convenient commands for managing the Ragnarok Online development environment

.PHONY: help build up down restart logs clean setup-db import-db backup-db restore-db

# Default target
help:
	@echo "RagnaDocker - Ragnarok Online Development Environment"
	@echo ""
	@echo "Available commands:"
	@echo "  make setup      - Initial setup (build and start all services)"
	@echo "  make up         - Start all services"
	@echo "  make down       - Stop all services"
	@echo "  make restart    - Restart all services"
	@echo "  make build      - Build all Docker images"
	@echo "  make logs       - Show logs from all services"
	@echo "  make clean      - Stop services and remove volumes (WARNING: deletes data)"
	@echo ""
	@echo "Database commands:"
	@echo "  make setup-db   - Initialize database and import rAthena schema"
	@echo "  make import-db  - Import rAthena database schema"
	@echo "  make backup-db  - Backup database to sql/backup.sql"
	@echo "  make restore-db - Restore database from sql/backup.sql"
	@echo ""
	@echo "Development commands:"
	@echo "  make rathena-shell  - Access rAthena container shell"
	@echo "  make db-shell       - Access MariaDB shell"
	@echo "  make rathena-logs   - Show rAthena server logs"
	@echo "  make fluxcp-logs    - Show FluxCP logs"
	@echo ""
	@echo "Web interfaces:"
	@echo "  make open-fluxcp    - Open FluxCP in browser"
	@echo "  make open-phpmyadmin - Open phpMyAdmin in browser"
	@echo "  make status         - Show service status and URLs"

# Initial setup
setup: build up setup-db
	@echo "Setup complete! Services are starting..."
	@echo "Wait a few minutes for all services to be ready, then:"
	@echo "- FluxCP: http://localhost:8080"
	@echo "- phpMyAdmin: http://localhost:8081"

# Build all images
build:
	docker-compose build

# Start all services
up:
	docker-compose up -d

# Stop all services
down:
	docker-compose down

# Restart all services
restart:
	docker-compose restart

# Show logs
logs:
	docker-compose logs -f

# Clean everything (WARNING: deletes data)
clean:
	docker-compose down -v
	docker system prune -f

# Database setup
setup-db:
	@echo "Waiting for database to be ready..."
	@sleep 30
	@echo "Importing rAthena database schema..."
	@make import-db

# Import rAthena database schema
import-db:
	docker-compose exec -T rathena bash -c " \
		if [ -f /rathena/sql-files/main.sql ]; then \
			mysql -h mariadb -u ragnarok -pragnarok_password ragnarok < /rathena/sql-files/main.sql && \
			mysql -h mariadb -u ragnarok -pragnarok_password ragnarok < /rathena/sql-files/logs.sql && \
			echo 'Database schema imported successfully!'; \
		else \
			echo 'rAthena SQL files not found. Please ensure rAthena source is properly mounted.'; \
		fi"

# Backup database
backup-db:
	@mkdir -p sql
	docker-compose exec -T mariadb mysqldump -u ragnarok -pragnarok_password ragnarok > sql/backup.sql
	@echo "Database backed up to sql/backup.sql"

# Restore database
restore-db:
	@if [ ! -f sql/backup.sql ]; then \
		echo "Backup file sql/backup.sql not found!"; \
		exit 1; \
	fi
	docker-compose exec -T mariadb mysql -u ragnarok -pragnarok_password ragnarok < sql/backup.sql
	@echo "Database restored from sql/backup.sql"

# Development helpers
rathena-shell:
	docker-compose exec rathena bash

db-shell:
	docker-compose exec mariadb mysql -u ragnarok -pragnarok_password ragnarok

rathena-logs:
	docker-compose logs -f rathena

fluxcp-logs:
	docker-compose logs -f fluxcp

# Service-specific commands
build-rathena:
	docker-compose build rathena

build-fluxcp:
	docker-compose build fluxcp

restart-rathena:
	docker-compose restart rathena

restart-fluxcp:
	docker-compose restart fluxcp

restart-db:
	docker-compose restart mariadb

# Web interface commands
open-fluxcp:
	@echo "Opening FluxCP in browser..."
	@echo "FluxCP URL: http://localhost:8080"
	@echo "Installation URL: http://localhost:8080/?module=install"
	@which xdg-open > /dev/null && xdg-open "http://localhost:8080" || \
	which open > /dev/null && open "http://localhost:8080" || \
	echo "Please open http://localhost:8080 in your browser"

open-phpmyadmin:
	@echo "Opening phpMyAdmin in browser..."
	@echo "phpMyAdmin URL: http://localhost:8081"
	@which xdg-open > /dev/null && xdg-open "http://localhost:8081" || \
	which open > /dev/null && open "http://localhost:8081" || \
	echo "Please open http://localhost:8081 in your browser"

status:
	@echo "=== RagnaDocker Service Status ==="
	@echo ""
	@docker-compose ps
	@echo ""
	@echo "=== Service URLs ==="
	@echo "FluxCP:           http://localhost:8080"
	@echo "FluxCP Install:   http://localhost:8080/?module=install"
	@echo "phpMyAdmin:       http://localhost:8081"
	@echo ""
	@echo "=== rAthena Server Ports ==="
	@echo "Login Server:     localhost:6900"
	@echo "Character Server: localhost:6121"
	@echo "Map Server:       localhost:5121"
	@echo ""
	@echo "=== Database Connection ==="
	@echo "Host:     localhost:3306"
	@echo "Database: ragnarok"
	@echo "Username: ragnarok"
	@echo "Password: ragnarok_password"
