<?php
// Commenting out a castle's entry will remove it from the castles page
// and exclude it from being a factor in the guild ranking.
return array(
	// iRO Names
	 0 => 'Neuschwanstein',
	 1 => 'Hohenschwangau',
	 2 => 'Nuenberg',
	 3 => 'Wuerzburg',
	 4 => 'Rothenburg',
	 5 => 'Repher<PERSON>',
	 6 => 'Eeyolbriggar',
	 7 => 'Yesnel<PERSON>',
	 8 => 'Bergel',
	 9 => 'Mersetzdeitz',
	10 => 'Bright Arbor',
	11 => 'Scarlet Palace',
	12 => 'Holy Shadow',
	13 => 'Sacred Altar',
	14 => 'Bamboo Grove Hill',
	15 => 'Kriemhild',
	16 => 'Swanhild',
	17 => 'Fadhgridh',
	18 => 'Skoegul',
	19 => 'Gondul',
	20 => 'Novice Aldebaran',
	21 => 'Novice Geffen',
	22 => 'Novice Payon',
	23 => 'Novice Prontera',
	24 => '<PERSON>inn',
	25 => 'Andlangr',
	26 => '<PERSON><PERSON><PERSON>n',
	27 => 'Hljod',
	28 => 'Skidbladnir',
	29 => '<PERSON><PERSON><PERSON>',
	30 => 'Cyr',
	31 => '<PERSON>',
	32 => 'Gefn',
	33 => '<PERSON><PERSON>',
	34 => '<PERSON><PERSON><PERSON>',
	35 => '<PERSON>vianne',
	36 => 'Jasmine',
	37 => 'Roxie',
	38 => 'Curly Sue',
	39 => 'Gaebolg',
	40 => 'Richard',
	41 => 'Wigner',
	42 => 'Heine',
	43 => 'Nerious'
	// kRO Names
	/**
	 * 0 => 'Noisyubantian',
	 * 1 => 'Hohensyubangawoo',
	 * 2 => 'Nyirenverk',
	 * 3 => 'Byirtsburi',
	 * 4 => 'Rotenburk',
	 * 5 => 'Reprion',
	 * 6 => 'Yolbriger',
	 * 7 => 'Isinlife',
	 * 8 => 'Berigel',
	 * 9 => 'Melsedetsu',
	 *10 => 'Mingting',
	 *11 => 'Tiantan',
	 *12 => 'Fuying',
	 *13 => 'Honglou',
	 *14 => 'Zhulinxian',
	 *15 => 'Creamhilt',
	 *16 => 'Sbanhealt',
	 *17 => 'Lazrigees',
	 *18 => 'Squagul',
	 *19 => 'Guindull',
	 *20 => 'Novice Aldebaran',
	 *21 => 'Novice Geffen',
	 *22 => 'Novice Payon',
	 *23 => 'Novice Prontera',
	 *24 => 'Himinn',
	 *25 => 'Andlangr',
	 *26 => 'Viblainn',
	 *27 => 'Hljod',
	 *28 => 'Skidbladnir',
	 *29 => 'Mardol',
	 *30 => 'Cyr',
	 *31 => 'Horn',
	 *32 => 'Gefn',
	 *33 => 'Bandis',
	 *34 => 'Kafragarten 1',
	 *35 => 'Kafragarten 2',
	 *36 => 'Kafragarten 3',
	 *37 => 'Kafragarten 4',
	 *38 => 'Kafragarten 5',
	 *39 => 'Gloria 1',
	 *40 => 'Gloria 2',
	 *41 => 'Gloria 3',
	 *42 => 'Gloria 4',
	 *43 => 'Gloria 5'
	 */
)
?>
