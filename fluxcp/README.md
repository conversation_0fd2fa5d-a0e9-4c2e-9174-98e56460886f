FluxCP
======
[![DeepSource](https://app.deepsource.com/gh/rathena/FluxCP.svg/?label=active+issues&show_trend=true&token=nhkIfid6qRIZxl2INWaaV4Qb)](https://app.deepsource.com/gh/rathena/FluxCP/?ref=repository-badge)
[![Open Issues](https://img.shields.io/github/issues/rathena/FluxCP.svg?logo=github&logoWidth=18&color=yellow)](https://lgtm.com/projects/g/rathena/FluxCP/alerts/)
[![Open PRs](https://img.shields.io/github/issues-pr/rathena/FluxCP.svg?logo=github&logoWidth=18&color=blue)](https://lgtm.com/projects/g/rathena/FluxCP/alerts/)
[![Codacy Badge](https://app.codacy.com/project/badge/Grade/4d1c0a43c0864764b3d3dc5ed2d93192)](https://www.codacy.com/gh/rathena/FluxCP/dashboard?utm_source=github.com&amp;utm_medium=referral&amp;utm_content=rathena/FluxCP&amp;utm_campaign=Badge_Grade)

Flux Control Panel (FluxCP) for rAthena servers.

Requirements
---------
* PHP 5.2
* PDO and PDO-MYSQL extensions for PHP5 (including PHP_MYSQL support)
* MySQL 5
* Optional: GD2 (for guild emblems and registration CAPTCHA)
* Optional: Tidy (for cleaner HTML output)
* Optional: mod_rewrite support for UseCleanUrls feature
* Optional: [Item images](http://rathena.org/board/files/file/2509-item-images/)


What's New?
---------
* The description files are kept upto-date with each new commit from rAthena.
* Pre-integrated themes:
	- default
	- Bootstrap

* Built-In:
	- News and Pages CMS with integrated TinyMCE
	- Service Desk (ITIL Incident Management style support ticket system)
	- Additional Logs
	- More Ranking Lists
    - Discord Integration
    - Google ReCaptcha
    - Remote AtCommand functionality


How To ... ?
---------
We have a small doc library that covers:
* Basic User Documentation
    - Installation
    - [Themes](https://github.com/rathena/FluxCP/blob/master/doc/user_theme.md)
    - [Languages](https://github.com/rathena/FluxCP/blob/master/doc/user_lang.md)
    - Installing Addons
    - Updating FluxCP

* Developer Documentation
    - Creating an Addon
    - Providing Addon updates
    - Creating a custom Theme


Join the Discussion
---------
We have a discord server separate from rAthena just for FluxCP stuff!
The channels there can be used to obtain help, discuss testing, view anonymous feedback log, Github commits, etc.
https://discord.gg/kMeMXWEvSV


Extra Credits
---------
Original FluxCP created by Paradox924X and Byteflux with additional contributions from Xantara.
Some works from other users have been integrated into this project.
