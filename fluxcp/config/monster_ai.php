<?php
return array(
	'01'			=> array('mode_canattack', 'mode_canmove'),
	'02'			=> array('mode_canattack', 'mode_looter', 'mode_canmove'),
	'03'			=> array('mode_changetargetmelee', 'mode_canattack', 'mode_assist', 'mode_canmove'),
	'04'			=> array('mode_changetargetchase', 'mode_changetargetmelee', 'mode_angry', 'mode_canattack', 'mode_aggressive', 'mode_canmove'),
	'05'			=> array('mode_changetargetchase', 'mode_canattack', 'mode_aggressive', 'mode_canmove'),
	'06'			=> array(),
	'07'			=> array('mode_changetargetmelee', 'mode_canattack', 'mode_assist', 'mode_looter', 'mode_canmove'),
	'08'			=> array('mode_targetweak', 'mode_changetargetchase', 'mode_changetargetmelee', 'mode_canattack', 'mode_aggressive', 'mode_canmove'),
	'09'			=> array('mode_changetargetchase', 'mode_changetargetmelee', 'mode_canattack', 'mode_castsensoridle', 'mode_aggressive', 'mode_canmove'),
	'10'			=> array('mode_canattack', 'mode_aggressive'),
	'11'			=> array('mode_canattack', 'mode_aggressive'),
	'12'			=> array('mode_changetargetchase', 'mode_canattack', 'mode_aggressive', 'mode_canmove'),
	'13'			=> array('mode_changetargetchase', 'mode_changetargetmelee', 'mode_canattack', 'mode_assist', 'mode_aggressive', 'mode_canmove'),
	//14			=> array(),
	//15			=> array(),
	//16			=> array(),
	'17'			=> array('mode_canattack', 'mode_castsensoridle', 'mode_canmove'),
	//18			=> array(),
	'19'			=> array('mode_changetargetchase', 'mode_changetargetmelee', 'mode_canattack', 'mode_castsensoridle', 'mode_aggressive', 'mode_canmove'),
	'20'			=> array('mode_changetargetchase', 'mode_changetargetmelee', 'mode_castsensorchase', 'mode_canattack', 'mode_castsensoridle', 'mode_aggressive', 'mode_canmove'),
	'21'			=> array('mode_changetargetchase', 'mode_changetargetmelee', 'mode_changechase', 'mode_castsensorchase', 'mode_canattack', 'mode_castsensoridle', 'mode_aggressive', 'mode_canmove'),
	//22			=> array(),
	//23			=> array(),
	'24'			=> array('mode_canattack', 'mode_norandomwalk', 'mode_canmove'),
	'25'			=> array('mode_canmove'),
	'26'			=> array('mode_randomtarget', 'mode_changetargetchase', 'mode_changetargetmelee', 'mode_changechase', 'mode_castsensorchase', 'mode_canattack', 'mode_castsensoridle', 'mode_aggressive', 'mode_canmove'),
	'27'			=> array('mode_randomtarget', 'mode_canattack', 'mode_aggressive'),
)
?>
