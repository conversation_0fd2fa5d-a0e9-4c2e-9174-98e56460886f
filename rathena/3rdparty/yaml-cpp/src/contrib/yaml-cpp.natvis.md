# MSVC debugger visualizer for YAML::Node

## How to use
Add yaml-cpp.natvis to your Visual C++ project like any other source file.  It will be included in the debug information, and improve debugger display on YAML::Node and contained types. 

## Compatibility and Troubleshooting

This has been tested for MSVC 2017. It is expected to be compatible with VS 2015 and VS 2019. If you have any problems, you can open an issue here: https://github.com/peterchen-cp/yaml-cpp-natvis

