<?php
// rAthena FluxCP Language Library
//
// Mempersembahkan, dukungan Bahasa Indonesia (id_id.php)
//
// Oleh:
// Cydh, nanakiwurtz, <YOU>

return array(
	// Generic/Misc.
	'Language'                => 'Indonesian',
	'YesLabel'				=> 'Ya',
	'NoLabel'				 => 'Tidak',
	'NoteLabel'			   => 'Catatan',
	'GenderTypeMale'		  => 'Pria',
	'GenderTypeFemale'		=> '<PERSON>ita',
	'GenderTypeServer'		=> 'Server',
	'RefreshSecurityCode'	 => 'Muat Ulang Kode Keamanan',
	'NoneLabel'			   => 'Kosong',
	'NeverLabel'			  => '<PERSON><PERSON>',
	'NotApplicableLabel'	  => 'Tidak Dapat Digunakan',
	'UnknownLabel'			=> 'Tidak Diketahui',
	'IsEqualToLabel'		  => 'sama dengan',
	'IsGreaterThanLabel'	  => 'lebih besar dari',
	'IsLessThanLabel'		 => 'lebih kecil dari',
	'AllLabel'				=> 'Semua',
	'SearchLabel'			 => 'Cari…',
	'GoBackLabel'			 => 'Ke<PERSON><PERSON> ke halaman sebelumnya…',
	'SearchButton'			=> 'Cari',
	'ResetButton'			 => 'Reset',
	'FilterButton'			=> 'Penyaringan',
	'NotAcceptingDonations'   => "Kami mohon maaf, tapi saat ini kami sedang tidak menerima donasi. Maaf untuk ketidaknyamanannya.",
	//'NotAcceptingDonations'   => "Kami mohon maaf, tapi sistem donasi kami saat ini sedang dalam perbaikan, mohon ulangi beberapa saat lagi.",
	'FoundSearchResults'	  => 'Ditemukan. Total %d dari %d halaman.  Menampilkan hasil %d-%d.',
	'LoginToDonate'		   => 'Harap login untuk melakukan donasi.',
	'UnknownCharacter'		=> 'Karakter tidak ditemukan.',
	'AccountIdLabel'		  => 'ID Akun',
	'AccountGroupIDLabel'	 => 'ID Grup',
	'AccountStateLabel'	   => 'State',
	'CreditBalanceLabel'	  => 'Saldo Kredit',
	'UsernameLabel'		   => 'Nama Pengguna',
	'PasswordLabel'		   => 'Sandi',
	'EmailAddressLabel'	   => 'E-mail',
	'GenderLabel'			 => 'Jenis Kelamin',
	'LoginCountLabel'		 => 'Jumlah Login', //bukan "Jumlah". xD jumlah = sum(), itu count() :P
	'LastLoginDateLabel'	  => 'Terakhir Login',
	'LastUsedIpLabel'		 => 'IP Terakhir',
	'AccountStateNormal'	  => 'Normal',
	'AccountStatePending'	 => 'Pending',
	'AccountStatePermBanned'  => 'Ban Permanen',
	'AccountStateTempBanLbl'  => 'Ban Sementara',
	'AccountStateTempBanned'  => 'Ban Sementara (unban: %s)',
	'OnlineLabel'			 => 'Online',
	'OfflineLabel'			=> 'Offline',
	'ItemIdLabel'			 => 'ID Barang',
	'ItemNameLabel'		   => 'Nama Barang',
	'ItemAmountLabel'		 => 'Jumlah',
	'ItemIdentifyLabel'	   => 'Teridentifikasi',
	'ItemRefineLabel'		 => 'Tempa',
	'ItemBrokenLabel'		 => 'Rusak',
	'ItemCard0Label'		  => 'Kartu 1',
	'ItemCard1Label'		  => 'Kartu 2',
	'ItemCard2Label'		  => 'Kartu 3',
	'ItemCard3Label'		  => 'Kartu 4',

	//SIDEBAR
	//FluxCP Menu Items
	//Categories
	'MainMenuLabel'		   => 'Menu Utama',
	'ForumLabel'			  => 'Forum',
	'AccountLabel'			=> 'Akun',
	'CharacterLabel'		  => 'Character',
	'ServiceDeskLabel'		=> 'Service Desk',
	'CPLogsLabel'			 => 'CP Logs',
	'FluxAdminLabel'		  => 'Flux Admin',
	'PagesLabel'			  => 'Pages',
	'IPBanListLabel'		  => 'IP Ban List',
	'GuildsLabel'			 => 'Guilds',
	'rALogsLabel'			 => 'rA Logs',
	'SendMailLabel'		   => 'Send Mail',
	'ReInstallLabel'		  => 'Re-Install',
	'TaskListLabel'		   => 'Task List',
	'DonationsLabel'		  => 'Donasi',
	'InformationLabel'		=> 'Informasi',
	'DatabaseLabel'		   => 'Database',
	'SocialLabel'			 => 'Social',
	//SubMenus
	'HomeLabel'			   => 'Beranda',
	'NewsLabel'			   => 'Berita',
	'DownloadsLabel'		  => 'Download',
	'RulesLabel'			  => 'Peraturan',
	'ContactUsLabel'		  => 'Hubungi Kami',
	'MyAccountLabel'		  => 'Akun Saya',
	'HistoryLabel'			=> 'History',
	'ServiceDeskLabel'		=> 'Bantuan',
	'PurchaseLabel'		   => 'Pembelian',
	'DonateLabel'			 => 'Berikan Donasi',
	'ServerInfoLabel'		 => 'Info Server',
	'ServerStatusLabel'	   => 'Status Server',
	'WoeHoursLabel'		   => 'Jadwal WOE',
	'CastlesLabel'			=> 'Kastil',
	'WhosOnlineLabel'		 => 'Pemain Online',
	'MapStaticsLabel'		 => 'Informasi Map',
	'RankingInfoLabel'		=> 'Informasi Peringkat',
	'VendingInfoLabel'		=> 'Informasi Vending',
	'ItemDatabaseLabel'	   => 'Database Barang',
	'MobDatabaseLabel'		=> 'Database Monster',
	'JoinUsInFacebookLabel'   => 'Kunjungi kami di Facebook!',
	'RateUsOnRMSLabel'		=> 'Kunjungi kami di RMS!',

	// Module: account
	// - account/changemail
	'EmailChangeTitle'		=> 'Ubah E-mail',
	'EnterEmailAddress'	   => 'Masukkan alamat e-mail.',
	'EmailCannotBeSame'	   => 'E-mail baru Anda tidak boleh sama dengan yang ada saat ini.',
	'EmailInvalid'			=> 'Alamat e-mail tidak diizinkan.',
	'EmailAlreadyRegistered'  => 'Alamat e-mail yang Anda masukkan sudah digunakan untuk akun lain',
	'EmailChangeSent'		 => 'Sebuah e-mail telah dikirim ke alamat e-mail Anda yang baru dengan tautan yang akan mengkonfirmasi penggantian.',
	'EmailAddressChanged'	 => 'Alamat e-mail Anda telah diubah!',
	'EmailChangeFailed'	   => 'Gagal untuk mengubah alamat e-mail. Harap ulangi beberapa saat lagi.',
	'EmailChangeHeading'	  => 'Ubah E-mail',
	'EmailChangeInfo'		 => 'Jika Anda ingin mengubah alamat e-mail, Anda dapat mengisi formulir di bawah ini.',
	'EmailChangeInfo2'		=> 'Setelah mengirim formulir, Anda akan diminta untuk mengkonfimasi alamat e-mail baru Anda (sebuah e-mail akan dikirimkan ke alamat e-mail baru berisi sebuah tautan).',
	'EmailChangeLabel'		=> 'Alamat e-mail baru',
	'EmailChangeInputNote'	=> 'Harus alamat e-mail yang benar!',
	'EmailChangeButton'	   => 'Ubah alamat e-mail',
	// - account/changepass
	'PasswordChangeTitle'	 => 'Ubah Sandi',
	'NeedCurrentPassword'	 => 'Masukkan sandi yang digunakan.',
	'NeedNewPassword'		 => 'Masukkan sandi baru.',
	'OldPasswordInvalid'	  => 'Kata sandi yang Anda berikan tidak sesuai.',
	'ConfirmNewPassword'	  => 'Konfirmasi kata sandi baru Anda.',
	'NewPasswordHasUsername'  => 'Tidak boleh ada nama pengguna di kata sandi yang baru.',
	'NewPasswordInvalid'	  => 'Terdapat karakter yang tidak diizinkan di kata sandi yang baru.',
	'NewPasswordSameAsOld'	=> 'Kata sandi yang baru tidak boleh sama dengan kata sandi yang ada saat ini.',
	'NewPasswordNeedUpper'	=> 'Kata sandi yang baru harus menggunakan minimal %d huruf kapital.',
	'NewPasswordNeedLower'	=> 'Kata sandi yang baru harus menggunakan minimal %d huruf kecil.',
	'NewPasswordNeedNumber'   => 'Kata sandi yang baru harus menggunakan minimal %d angka.',
	'NewPasswordNeedSymbol'   => 'Kata sandi yang baru harus menggunakan minimal %d simbol.',
	'PasswordHasBeenChanged'  => 'Kata sandi Anda telah diubah, silakan login kembali.',
	'FailedToChangePassword'  => 'Penggantian kata sandi tidak berhasil. Harap menghubungi admin.',
	'PasswordChangeHeading'   => 'Ubah Kata Sandi',
	'PasswordChangeInfo'	  => 'Silakan masukkan kata sandi Anda, lalu masukkan kata sandi yang baru.',
	'CurrentPasswordLabel'	=> 'Kata Sandi Saat Ini',
	'NewPasswordLabel'		=> 'Kata Sandi Baru',
	'NewPasswordConfirmLabel' => 'Konfirmasi Kata Sandi Baru',
	'PasswordChangeNote'	  => 'Harap gunakan informasi yang benar.',
	'PasswordChangeNote2'	 => 'Setelah mengganti kata sandi, Anda akan logout.',
	'PasswordChangeButton'	=> 'Ganti Kata Sandi',
	// - account/changesex
	'GenderChangeTitle'	   => 'Ubah Jenis Kelamin', //swt -_-
	'GenderChangeBadChars'	=> 'Anda tidak dapat mengubah jenis kelamin jika ada karakter yang Anda miliki: %s',
	'GenderChanged'		   => 'Jenis kelamin Anda telah diganti dan %d kredit telah dikurangi dari akun Anda.',
	'GenderChangedForFree'	=> 'Jenis kelamin Anda telah diganti.',
	'GenderChangeHeading'	 => 'Ubah Jenis Kelamin',
	'GenderChangeCost'		=> 'Biaya penggantian jenis kelamin = %s kredit.',
	'GenderChangeBalance'	 => 'Kredit yang Anda miliki = %s .',
	'GenderChangeNoFunds'	 => 'Anda tidak memiliki kredit yang cukup untuk mengganti jenis kelamin.',
	'GenderChangeNoCost'	  => 'Untuk Anda, gratis untuk mengganti jenis kelamin.',
	'GenderChangeCharInfo'	=> 'Anda tidak dapat mengganti jenis kelamin jika Anda memiliki karakter dengan job: %s',
	'GenderChangeSubHeading'  => 'Yakinkan jika Anda ingin benar-benar mengganti!',
	'GenderChangeFormText'	=> 'Akankah Anda mengganti jenis kelamin menjadi %s?',
	'GenderChangeConfirm'	 => 'Apa Anda yakin untuk mengganti jenis kelamin Anda?',
	'GenderChangeButton'	  => 'Ya.',
	// - account/confirm
	'AccountConfirmTitle'	 => 'Konfirmasi akun',
	'AccountConfirmUnban'	 => 'Akun telah dikonfirmasi dan diaktifkan.',
	'AccountConfirmMessage'   => 'Akun anda telah dikonfirmasi dan diaktifkan, Anda dipersilahkan untuk log-in.',
	// - account/confirmemail
	'EmailConfirmTitle'	   => 'Konfirmasi E-mail',
	'EmailConfirmFailed'	  => 'Terdapat masalah teknis ketika memperbaruhi alamat e-mail baru, harap hubungi admin.',
	'EmailConfirmChanged'	 => 'Alamat e-mail Anda telah diganti!',
	// - account/create
	'AccountCreateTitle'	  => 'Buat Akun',
	'AccountConfirmBan'	   => 'Menunggu aktivasi akun: %s',
	'AccountCreateEmailSent'  => 'Sebuah e-mail telah dikirimkan dan berisi keterangan tentang detail akun Anda, harap periksa e-mail Anda untuk aktifasi.',
	'AccountCreateFailed'	 => 'Akun Anda telah dibuat, tetapi kami gagal untuk mengirimkan e-mail kepada Anda. Harap hubungi admin untuk mendapatkan bantuan.',
	'AccountCreated'		  => 'Selamat, Anda telah terdaftar dan login secara otomatis.',
	'AccountCreateHeading'	=> 'Pendaftaran Akun Baru',
	'AccountCreateTerms'	  => 'Syarat dan Ketentuan',
	'AccountCreateInfo'	   => 'Anda diharapkan untuk membaca %s sebelum membuat akun baru, untuk memastikan bahwa Anda mengerti semua ketentuan  yang berlaku di private server kami.',
	'AccountCreateInfo2'	  => 'Dengan menekan tombol "Buat Akun Baru", Anda setuju untuk taat dan mengikuti %s kami.',
	'AccountCreateGenderInfo' => "Jenis kelamin yang Anda pilih akan menentukan karakter Anda di dalam game!",
	'AccountServerLabel'	  => 'Server',
	'AccountUsernameLabel'	=> 'Nama Pengguna',
	'AccountPasswordLabel'	=> 'Kata Sandi',
	'AccountPassConfirmLabel' => 'Konfirmasi Kata Sandi',
	'AccountEmailLabel'	   => 'Alamat E-mail',
	'AccountEmailLabel2'	  => 'Konfirmasi alamat e-mail',
	'AccountGenderLabel'	  => 'Jenis Kelamin',
	'AccountBirthdateLabel'   => 'Tanggal Lahir',
	'AccountSecurityLabel'	=> 'Kode Keamanan',
	'AccountCreateButton'	 => 'Buat Akun Baru', // "Create My Account?" bukan "new" ya?
	'AccountInvalidChars'	 => "Nama pengguna hanya diperbolehkan menggunakan karakter: '%s'",
	'InvalidLoginServer'	  => 'Login-server salah, ulangi lagi dengan memilih login-sevrer yang benar.',
	'InvalidLoginCredentials' => 'Data login tidak sesuai, verifikasi data yang dimasukkan dan ulangi.',
	'UnexpectedLoginError'	=> 'Terjadi kesalahan yang tidak diinginkan, ulangi beberapa saat lagi atau laporkan kepada admin.',
	'CriticalLoginError'	  => 'Sesuatu yang buruk telah terjadi.  Laporkan ke administartor SEGERA!',
	'UsernameAlreadyTaken'	=> 'Nama pengguna yang Anda pilih sudah digunakan oleh orang lain',
	'UsernameTooShort'		=> sprintf('Nama pengguna Anda harus %d sampai %d karakter.', Flux::config('MinUsernameLength'), Flux::config('MaxUsernameLength')),
	'UsernameTooLong'		 => sprintf('Nama pengguna Anda harus %d sampai %d karakter.', Flux::config('MinUsernameLength'), Flux::config('MaxUsernameLength')),
	'PasswordContainsUser'	=> 'Tidak boleh terdapat nama pengguna di kata sandi.',
	'PasswordHasUsername'	 => 'Tidak boleh terdapat nama pengguna di kata sandi.',
	'PasswordTooShort'		=> 'Kata sandi harus %d sampai %d karakter.',
	'PasswordTooLong'		 => 'Kata sandi harus %d sampai %d karakter.',
	'PasswordsDoNotMatch'	 => 'Kata sandi tidak cocok, harap masukkan kedua kata sandi dengan benar',
	'PasswordNeedUpper'	   => 'Kata sandi harus memiliki huruf BESAR setidaknya %d huruf.',
	'PasswordNeedLower'	   => 'Kata sandi harus memiliki huruf kecil setidaknya %d huruf.',
	'PasswordNeedNumber'	  => 'Kata sandi harus memiliki angka setidaknya %d digit.',
	'PasswordNeedSymbol'	  => 'Kata sandi harus memiliki simbol setidaknya %d simbol.',
	'EmailAddressInUse'	   => 'Alamat e-mail yang Anda masukkan sudah digunakan oleh pengguna lain. Masukkan alamat e-mail yang lain',
	'InvalidEmailAddress'	 => 'Alamat e-mail yang Anda masukkan memiliki format yang tidak diizinkan',
	'InvalidEmailconf'		=> "Alamat e-mail Anda tidak cocok.",
	'InvalidGender'		   => 'Jenis kelamin yang diperbolehkan adalah "M" atau "F"',
	'InvalidServer'		   => "Server yang Anda pilih tidak tersedia.",
	'InvalidSecurityCode'	 => 'Silakan masukkan kode keamanan dengan benar.',
	'InvalidPassword'		 => 'Kata sandi Anda mengandung karakter yang tidak diperbolehkan.',
	'InvalidBirthdate'		=> 'Tanggal lahir Anda tidak benar.',
	'CriticalRegisterError'   => 'Error. Harap hubungi admin secepatnya.',
	// - account/edit
	'AccountEditTitle'		=> 'Ubah Akun',
	'AccountEditTitle2'	   => 'Ubah Akun Saya',
	'AccountEditTitle3'	   => 'Mengubah Akun (%s)',
	'CannotModifyOwnGroupID'  => 'Anda tidak bisa mengubah grup ID Anda sendiri.',
	'CannotModifyAnyGroupID'  => 'Anda tidak bisa mengubah grup ID.',
	'CannotModifyGroupIDHigh' => 'Anda tidak bisa mengubah grup ID menjadi ID yang lebih tinggi dari akun Anda sendiri.',
	'InvalidGroupID'		  => 'Grup ID salah.',
	'CannotModifyBalance'	 => 'Anda tidak bisa mengubah jumlah kredit.',
	'InvalidLastLoginDate'	=> 'Tanggal login terakhir salah.',
	'InvalidVIPTime'		  => 'Waktu VIP salah.',
	'AccountModified'		 => 'Akun telah berhasil diubah.',
	'AccountEditHeading'	  => 'Ubah Akun',
	'AccountEditButton'	   => 'Ubah Akun',
	'AccountEditNotFound'	 => 'Akun tidak ditemukan.',
	'VIPTimeDateLabel'		=> 'VIP Aktif Hingga',
	// - account/index
	'AccountIndexTitle'	   => 'Daftar Akun',
	'AccountIndexHeading'	 => 'Akun',
	'LoginBetweenLabel'	   => 'Login di antara',
	'BirthdateBetweenLabel'   => 'Tanggal lahir di antara',
	'AccountIndexNotFound'	=> 'Akun tidak ditemukan.',
	// - account/login
	'LoginTitle'			  => 'Masuk Akun',
	'LoginHeading'			=> 'Masuk Akun',
	'LoginButton'			 => 'Masuk',
	'LoginPageMakeAccount'	=> 'Belum punya akun? <a href="%s">Daftarkan akun baru!</a>',
	'TemporarilyBanned'	   => 'Akun Anda telah diban untuk sementara.',
	'PermanentlyBanned'	   => 'Akun Anda telah diban secara permanen.',
	'IpBanned'				=> 'Alamat IP Anda telah diban.',
	'PendingConfirmation'	 => 'Akun Anda masih menunggu aktivasi.',
	// - account/logout
	'LogoutTitle'			 => 'Logout',
	'LogoutHeading'		   => 'Logout',
	'LogoutInfo'			  => 'Anda telah logged out.',
	'LogoutInfo2'			 => 'Harap tunggu beberapa saat, halaman sedang <a href="%s">dialihkan</a>…',
	// - account/resend
	'ResendTitle'			 => 'Kirim Ulang E-mail Konfirmasi',
	'ResendEnterUsername'	 => 'Masukkan nama pengguna anda.',
	'ResendEnterEmail'		=> 'Masukkan alamat e-mail.',
	'ResendFailed'			=> 'Gagal mengirim ulang kode konfirmasi.',
	'ResendEmailSent'		 => 'Kode konfirmasi anda telah dikirim ulang, silahkan cek e-mail dan ikuti langkah berikutnya untuk mengaktifkan akun.',
	'ResendHeading'		   => 'Kirim Ulang E-mail Konfirmasi',
	'ResendInfo'			  => 'Masukkan nama pengguna dan alamat e-mail yang digunakan ketika melakukan pendaftaran untuk pengiriman ulang e-mail konfirmasi',
	'ResendServerLabel'	   => 'Server Terdaftar',
	'ResendAccountLabel'	  => 'Nama Pengguna',
	'ResendEmailLabel'		=> 'Alamat E-Mail',
	'ResendServerInfo'		=> 'Server tempat akun didaftarkan.',
	'ResendAccountInfo'	   => 'Nama pengguna yang didaftarkan.',
	'ResendEmailInfo'		 => 'Alamat e-mail ini digunakan ketika Anda melakukan pendaftaran akun di atas.',
	'ResendButton'			=> 'Kirim Ulang E-Mail Konfirmasi',
	// - account/resetpass
	'ResetPassTitle'		  => 'Reset Kata Sandi',
	'ResetPassEnterAccount'   => 'Masukkan nama pengguna.',
	'ResetPassEnterEmail'	 => 'Masukkan alamat e-mail.',
	'ResetPassDisallowed'	 => 'Pemulihan kata sandi tidak dapat digunakan untuk akun ini.',
	'ResetPassFailed'		 => 'Gagal untuk mengirim e-mail pemulihan akun.',
	'ResetPassEmailSent'	  => 'E-mail berisi detail untuk menyetel ulang kata sandi sudah dikiriman.',
	'ResetPassInfo'		   => 'Jika Anda lupa kata sandi, Anda dapat menyetel ulang dengan cara memasukkan alamat e-mail yang digunakan ketika mendaftarkan akun.',
	'ResetPassInfo2'		  => 'Alamat e-mail harus benar untuk melakukan pengiriman sebuah e-mail ke alamat yang dicantumkan yang berisi tautan untuk menyetel ulang kata sandi akun Anda.',
	'ResetPassServerLabel'	=> 'Server Terdaftar',
	'ResetPassAccountLabel'   => 'Nama Pengguna',
	'ResetPassEmailLabel'	 => 'Alamat E-Mail',
	'ResetPassServerInfo'	 => 'Server tempat akun didaftarkan.',
	'ResetPassAccountInfo'	=> 'Nama pengguna yang didaftarkan.',
	'ResetPassEmailInfo'	  => 'Alamat e-mail ini digunakan ketika Anda melakukan pendaftaran akun di atas.',
	'ResetPassButton'		 => 'Kirim E-mail Penyetelan Ulang Kata Sandi',
	// - account/resetpw
	'ResetPwTitle'			=> 'Reset Kata Sandi',
	'ResetPwFailed'		   => 'Gagal untuk menyetel ulang kata sandi, ulangi beberapa saat lagi.',
	'ResetPwDone'			 => 'Kata sandi telah disetel ulang. Sebuah e-mail berisi kata sandi baru telah dikirimkan, silahkan cek e-mail Anda.',
	'ResetPwDone2'			=> 'Kata sandi sudah disetel ulang, tetapi e-mail yang berisi kata sandi baru gagal untuk dikirim. Silahkan ulangi menyetel ulang kata sandi.',
	// - account/transfer
	'TransferTitle'		   => 'Transfer Kredit',
	'TransferGreaterThanOne'  => 'Anda harus mengirimkan jumlah kredit yang lebih besar dari 1.',
	'TransferEnterCharName'   => 'Anda harus mengisi nama karakter penerima kredit.',
	'TransferNoCharExists'	=> "Karakter '%s' tidak ditemukan. Harap periksa kembali nama karakter tersebut.",
	'TransferNoBalance'	   => 'Anda tidak memiliki jumlah kredit yang cukup untuk melakukan transfer.',
	'TransferUnexpectedError' => 'Error! Harap hubungi admin.',
	'TransferSuccessful'	  => 'Kredit telah dikirim!',
	'TransferHeading'		 => 'Transfer Kredit',
	'TransferSubHeading'	  => 'Kredit akan ditransfer kepada karakter di server %s .',
	'TransferInfo'			=> 'Kredit sekarang %s .',
	'TransferInfo2'		   => 'Masukkan jumlah yang ingin Anda transfer dan nama karakter yang berada di akun yang ingin Anda tuju.',
	'TransferAmountLabel'	 => 'Jumlah Kredit',
	'TransferCharNameLabel'   => 'Nama Karakter',
	'TransferAmountInfo'	  => 'Jumlah kredit yang akan Anda kirim.',
	'TransferCharNameInfo'	=> 'Nama karakter penerima kredit.',
	'TransferConfirm'		 => 'Apakah Anda yakin?',
	'TransferButton'		  => 'Transfer',
	'TransferNoCredits'	   => 'Anda tidak memiliki kredit di akun Anda.',
	// - account/view
	'VIPStateLabel'		   => 'Status VIP',
	// * account/view submenus
	'ModifyAccountLink'	   => 'Ubah Akun',
	'AccountViewTitle'		=> 'Lihat Akun',
	'AccountViewTitle2'	   => 'Sedang Melihat Akun (%s)',
	'AccountViewTitle3'	   => 'Akun Saya',
	'AccountTempBanFailed'	=> 'Gagal melakukan ban sementara pada akun.',
	'AccountPermBanFailed'	=> 'Gagal melakukan ban permanen pada akun.',
	'AccountTempBanUnauth'	=> 'Anda tidak dapat melakukan ban sementara pada akun ini.',
	'AccountPermBanUnauth'	=> 'Anda tidak dapat melakukan ban permanen pada akun ini.',
	'AccountLiftTempBan'	  => 'Ban sementara pada akun ini telah dibatalkan.',
	'AccountLiftPermBan'	  => 'Ban permanen pada akun ini telah dibatalkan.',
	'AccountLiftBanUnauth'	=> "Anda tidak dapat menghapus status ban pada akun ini.",
	'AccountViewHeading'	  => 'Melihat Akun',
	'AccountViewDonateLink'   => '(Donasi!)',
	'AccountViewTempBanLabel' => 'Ban Sementara',
	'AccountViewPermBanLabel' => 'Ban Permanen',
	'AccountViewUnbanLabel'   => 'Hapus Ban',
	'AccountBanReasonLabel'   => 'Alasan:',
	'AccountBanUntilLabel'	=> 'Ban Sampai:',
	'AccountTempBanButton'	=> 'Ban Sementara',
	'AccountPermBanButton'	=> 'Ban Permanen',
	'AccountTempUnbanButton'  => 'Hapus Ban Sementara',
	'AccountPermUnbanButton'  => 'Hapus Ban Permanen',
	'AccountBanConfirm'	   => 'Apakah Anda yakin?',
	'AccountBanLogSubHeading' => 'Catatan Ban Untuk %s (Baru ke Lama)',
	'BanLogBanTypeLabel'	  => 'Jenis Ban',
	'BanLogBanDateLabel'	  => 'Tanggal Ban',
	'BanLogBanReasonLabel'	=> 'Alasan Ban',
	'BanLogBannedByLabel'	 => 'Pemberi Ban',
	'BanLogBannedByCP'		=> 'Control Panel',
	'BanTypeUnbanned'		 => 'Tidak di Ban',
	'BanTypePermBanned'	   => 'Ban Permanen',
	'BanTypeTempBanned'	   => 'Ban Sementara',
	'AccountViewCharSubHead'  => 'Karakter Pada %s',
	'AccountViewSlotLabel'	=> 'Slot',
	'AccountViewCharLabel'	=> 'Nama Karakter',
	'AccountViewClassLabel'   => 'Job Class',
	'AccountViewLvlLabel'	 => 'Base Level',
	'AccountViewJlvlLabel'	=> 'Job Level',
	'AccountViewZenyLabel'	=> 'Zeny',
	'AccountViewGuildLabel'   => 'Guild',
	'AccountViewStatusLabel'  => 'Status',
	'AccountViewPrefsLabel'   => 'Pengaturan',
	'CharModifyPrefsLink'	 => 'Ubah Pengaturan',
	'AccountViewNoChars'	  => 'Akun ini tidak memiliki karakter di %s.',
	'AccountViewStorage'	  => 'Penyimpanan Barang Milik %s',
	'AccountViewStorageCount' => '%s memiliki %s barang di penyimpanan.',
	'AccountViewNoStorage'	=> 'Tidak ada apapun di dalam penyimpanan barang.',
	'AccountViewNotFound'	 => "Akun yang Anda cari tidak ditemukan.",
	// - account/xferlog
	'XferLogTitle'			=> 'Catatan Transfer Kredit',
	'XferLogHeading'		  => 'Catatan Transfer Kredit',
	'XferLogReceivedSubHead'  => 'Transfer: Diterima',
	'XferLogSentSubHead'	  => 'Transfer: Dikirim',
	'XferLogCreditsLabel'	 => 'Kredit',
	'XferLogFromLabel'		=> 'Dari E-mail',
	'XferLogDateLabel'		=> 'Tanggal Transfer',
	'XferLogCharNameLabel'	=> 'Untuk Karakter',
	'XferLogNotReceived'	  => 'Anda tidak menerima pengiriman kredit.',
	'XferLogNotSent'		  => 'Anda tidak mengirim pengiriman kredit.',

	// Module: character
	// - character/changeslot
	// - character/index
	// - character/mapstats
	// - character/online
	// - character/prefs
	// - character/resetlook
	'CantResetLookWhenOnline' => 'Tidak dapat menyetel ulang penampilan %s ketika sedang online.',
	'ResetLookSuccessful'	 => 'Penampilan %s telah disetel ulang!',
	'ResetLookFailed'		 => 'Gagal untuk menyetel ulang penampilan %s.',
	// - character/resetpos
	'CantResetPosWhenOnline'  => 'Tidak dapat menyetel ulang posisi %s ketika sedang online.',
	'CantResetFromCurrentMap' => 'Anda tidak dapat menyetel ulang posisi %s di map saat ini.',
	'ResetPositionSuccessful' => 'Posisi %s telah disetel ulang!',
	'ResetPositionFailed'	 => 'Gagal untuk menyetel ulang posisi %s.',
	// - character/view
	// - character/divorce
	'DivorceTitle'			=> 'Bercerai',
	'DivorceHeading'		  => 'Bercerai',
	'DivorceNotMarried'	   => '%s belum menikah dengan siapapun.',
	'DivorceInvalidPartner'   => 'ID pasangan tidak ditemukan.',
	'DivorceInvalidChild'	 => 'ID adopsi tidak ditemukan.',
	'DivorceMustBeOffline'	=> '%s dan pasangannya harus dalam keadaan offline.',
	'DivorceMustBeOffline2'   => '%s, pasangan, dan anaknya harus dalam keadaan offline.',
	'DivorceText1'			=> "Apakah Anda ingin menceraikan dengan %s dan pasangannya?",
	'DivorceText2'			=> 'Apabila %s memiliki adopsi, status adopsi akan hilang.',
	'DivorceText3'			=> "Item 'Wedding ring' akan dihapus.",
	'DivorceButton'		   => 'Ya.',
	'DivorceSuccessful'	   => '%s telah bercerai!',

	// Module: cplog
	// - cplog/index.php
	// - cplog/login.php
	// - cplog/paypal.php
	// - cplog/resetpass.php
	// - cplog/txnview.php

	// Module: donate
	// - donate/complete
	// - donate/history
	// - donate/index
	// - donate/trusted

	// Module: errors
	// - errors/missing_action
	'MissingActionTitle'	  => 'Aksi Tidak Ditemukan',
	'MissingActionHeading'	=> 'Aksi Tidak Ditemukan!',
	'MissingActionModLabel'   => 'Modul:',
	'MissingActionActLabel'   => 'Aksi:',
	'MissingActionReqLabel'   => 'URI Diminta:',
	'MissingActionLocLabel'   => 'Lokasi file:',
	// - errors/missing_view
	'MissingViewTitle'		=> 'View Tidak Ditemukan',
	'MissingViewHeading'	  => 'View Tidak Ditemukan!',
	'MissingViewModLabel'	 => 'Modul:',
	'MissingViewActLabel'	 => 'Aksi:',
	'MissingViewReqLabel'	 => 'URI Diminta:',
	'MissingViewLocLabel'	 => 'Lokasi file:',

	// Module: guild
	// - guild/export
	// - guild/index
	// - guild/view

	// Module: history
	// - history/cplogin
	'HistoryCpLoginTitle'	 => 'Login Control Panel',
	'HistoryCpLoginHeading'   => 'Login Control Panel',
	'HistoryLoginDateLabel'   => 'Waktu/Tanggal Login',
	'HistoryIpAddrLabel'	  => 'Alamat IP',
	'HistoryErrorCodeLabel'   => 'Error Code',
	'HistoryNoCpLogins'	   => 'Tidak ada percobaan login ke Control Panel.',
	// -history/emailchange
	'HistoryEmailTitle'	   => 'Perubahan E-Mail',
	'HistoryEmailHeading'	 => 'Perubahan E-Mail',
	'HistoryEmailRequestDate' => 'Waktu/Tanggal Permohonan',
	'HistoryEmailRequestIp'   => 'IP Pemohon',
	'HistoryEmailOldAddress'  => 'E-Mail Lama',
	'HistoryEmailNewAddress'  => 'E-Mail Baru',
	'HistoryEmailChangeDate'  => 'Tanggal Perubahan',
	'HistoryEmailChangeIp'	=> 'IP Perubahan',
	'HistoryNoEmailChanges'   => 'Tidak ada percobaan perubahan e-mail.',
	// - history/gamelogin
	'HistoryGameLoginTitle'   => 'Masuk ke Game',
	'HistoryGameLoginHeading' => 'Masuk ke Game',
	'HistoryRepsCodeLabel'	=> 'Kode Respon',
	'HistoryLogMessageLabel'  => 'Catatan',
	'HistoryNoGameLogins'	 => 'Tidak ada percobaan Masuk ke Game.',
	// - history/index
	'HistoryIndexTitle'	   => 'Aktivitas Akun',
	'HistoryIndexHeading'	 => 'Aktivitas Akun',
	'HistoryIndexInfo'		=> 'Anda dapat melihat aktivitas yang telah lalu untuk akun Anda.',
	'HistoryIndexInfo2'	   => 'Harap pilih aksi pada menu.',
	// - history/passchange
	'HistoryPassChangeTitle'	  => 'Perubahan Kata Sandi',
	'HistoryPassChangeHeading'	=> 'Perubahan Kata Sandi',
	'HistoryPassChangeChangeDate' => 'Tanggal Perubahan',
	'HistoryPassChangeChangeIp'   => 'IP Perubahan',
	'HistoryNoPassChanges'		=> 'Tidak ada perubahan kata sandi.',
	// -history/passreset
	'HistoryPassResetTitle'	   => 'Penyetelan Ulang Kata Sandi',
	'HistoryPassResetHeading'	 => 'Penyetelan Ulang Kata Sandi',
	'HistoryPassResetRequestDate' => 'Tanggal Meminta',
	'HistoryPassResetRequestIp'   => 'IP Peminta',
	'HistoryPassResetResetDate'   => 'Tanggal Penyetelan',
	'HistoryPassResetResetIp'	 => 'IP Penyetelan',
	'HistoryNoPassResets'		 => 'Tidak ada percobaan merubah kata sandi.',

	// Module: ipban
	// - ipban/add
	'IpbanAddTitle'		   => 'Tambah Ban IP',
	'IpbanEnterIpPattern'	 => 'Harap masukkan alamat IP atau pola.',
	'IpbanInvalidPattern'	 => 'Alamat IP atau pola tidak sesuai.',
	'IpbanWhitelistedPattern' => 'Pola ini ada di whitelist dan tidak dapat diblokir.',
	'IpbanEnterReason'		=> 'Harap masukkan alasan',
	'IpbanSelectUnbanDate'	=> 'Tanggal unban dibutuhkan.',
	'IpbanFutureDate'		 => 'Tanggal unban harus tanggal yang akan datang.',
	'IpbanAlreadyBanned'	  => 'IP yang sesuai dengan (%s) telah diban.',
	'IpbanPatternBanned'	  => "Pola/alamat IP '%s' telah diban.",
	'IpbanAddFailed'		  => 'Gagal menambahkan IP yang diban.',
	'IpbanAddHeading'		 => 'Tambahkan Ban IP',
	'IpbanIpAddressLabel'	 => 'Alamat IP',
	'IpbanReasonLabel'		=> 'Alasan Ban',
	'IpbanUnbanDateLabel'	 => 'Tanggal Unban',
	'IpbanIpAddressInfo'	  => 'Anda boleh memasukkan pola serperti 218.139.*.*',
	'IpbanAddButton'		  => 'Tambahkan Ban IP',
	// - ipban/edit
	'IpbanEditTitle'		  => 'Ubah Ban IP',
	'IpbanEnterEditReason'	=> 'Harap masukkan alasan untuk perubahan ban IP',
	'IpbanEditFailed'		 => 'Gagal untuk mengubah ban IP.',
	'IpbanEditHeading'		=> 'Ubah Ban IP',
	'IpbanEditReasonLabel'	=> 'Ubah Alasan',
	'IpbanEditButton'		 => 'Modifikasi Ban IP',
	// - ipban/index
	'IpbanListTitle'		  => 'Daftar Ban IP',
	'IpbanListHeading'		=> 'Daftar Ban IP Ban',
	'IpbanBannedIpLabel'	  => 'Banned IP',
	'IpbanBanDateLabel'	   => 'Tanggal Ban',
	'IpbanBanReasonLabel'	 => 'Alasan Ban',
	'IpbanBanExpireLabel'	 => 'Tanggal Berakhir Ban',
	'IpbanModifyLink'		 => 'Ubah',
	'IpbanRemoveLink'		 => 'Hilangkan',
	'IpbanUnbanButton'		=> 'Unban yang dipilih',
	'IpbanListNoBans'		 => 'Tidak ada ban IP saat ini.',
	// - ipban/remove
	'IpbanRemoveTitle'		=> 'Hilangkan Ban IP',
	'IpbanEnterRemoveReason'  => 'Harap masukkan alasan untuk penghilangan ban IP.',
	'IpbanNotBanned'		  => 'IP (%s) tidak ditemukan.',
	'IpbanPatternUnbanned'	=> "Pola/alamat IP '%s' telah di-unban.",
	'IpbanRemoveFailed'	   => 'Gagal untuk menghilangkan ban IP.',
	'IpbanRemoveHeading'	  => 'Hilangkan Ban IP',
	'IpbanRemoveReasonLabel'  => 'Alasan Unban',
	'IpbanRemoveButton'	   => 'Hilangkan Ban IP',
	// - ipban/unban
	'IpbanNothingToUnban'	 => 'Tidak ada yang di-unban.',
	'IpbanEnterUnbanReason'   => 'Harap masukkan alasan untuk pengangkatan ban IP.',
	'IpbanUnbanned'		   => 'Ban IP telah dihilangkan!',
	'IpbanUnbanFailed'		=> 'Gagal untuk menghilangkan %d ban IP!',

	// Module: item
	// - item/add
	// - item/copy
	// - item/edit
	// - item/index
	// - item/view

	// Module: itemshop
	// - itemshop/add
	// - itemshop/delete
	// - itemshop/edit
	// - itemshop/imagedel

	// Module: logdata
	// - logdata/chat
	// - logdata/cashpoints
	'CashLogTitle'	 => 'Daftar Log CashPoints',
	'CashLogHeading'	 => 'Log CashPoint',
	'CashLogNotFound'	 => 'Tidak ada log CashPoint.',
	'CashLogDateLabel'	 => 'Tanggal/Jam',
	'CashLogCharacterLabel'	 => 'Karakter',
	'CashLogTypeLabel'	 => 'Tipe',
	'CashLogCashTypeLabel'	 => 'Tipe Cash',
	'CashLogAmountLabel'	 => 'Jumlah',
	'CashLogMapLabel'	 => 'Map',
	// - logdata/command
	'CommandLogTitle'		 => 'Daftar Perintah',
	'CommandLogHeading'	   => 'Perintah',
	'CommandLogNotFound'	  => 'Perintah tidak ditemukan.',
	'CommandLogDateLabel'	 => 'Tanggal/Jam',
	'CommandLogAccountIdLabel'=> 'ID Akun',
	'CommandLogCharIdLabel'   => 'ID Karakter',
	'CommandLogCharNameLabel' => 'Nama Karakter',
	'CommandLogCommandLabel'  => 'Perintah',
	'CommandLogMapLabel'	  => 'Map',
	// - logdata/index
	// - logdata/login
	// - logdata/pick
	'PickLogTitle'			=> 'Daftar Barang yang Diambil',
	'PickLogHeading'		  => 'Barang Diambil',
	'PickLogNotFound'		 => 'Tidak ada barang yang diambil.',
	'PickLogDateLabel'		=> 'Tanggal/Jam',
	'PickLogCharacterLabel'   => 'Karakter',
	'PickLogTypeLabel'		=> 'Tipe',
	'PickLogItemLabel'		=> 'Nama Barang',
	'PickLogAmountLabel'	  => 'Jumlah',
	'PickLogRefineLabel'	  => 'Tempa',
	'PickLogCard0Label'	   => 'Kartu 1',
	'PickLogCard1Label'	   => 'Kartu 2',
	'PickLogCard2Label'	   => 'Kartu 3',
	'PickLogCard3Label'	   => 'Kartu 4',
	'PickLogMapLabel'		 => 'Map',
	//		- logdata/branch
	'BranchLogTitle'			=> 'Daftar Branch',
	'BranchLogHeading'		  => 'Branch Log',
	'BranchLogNotFound'		 => 'Tidak ada log branch yang ditemukan.',
	'BranchLogIDLabel'		  => 'ID Branch Log',
	'BranchLogDateLabel'		=> 'Tanggal/Jam',
	'BranchLogAccountIDLabel'   => 'ID Akun',
	'BranchLogCharIDLabel'	  => 'ID Karakter',
	'BranchLogCharNameLabel'	=> 'Nama Karakter',
	'BranchLogMapLabel'		 => 'Map',
	// - logdata/char
	'CharLogTitle'			=> 'Dafatr Log Karakter',
	'CharLogHeading'		  => 'Log Karakter',
	'CharLogNotFound'		 => 'Tidak ada log karakter yang ditemukan.',
	'CharLogDateLabel'		=> 'Tanggal/Jam',
	'CharLogMsgLabel'		 => 'Aksi',
	'CharLogAccountIDLabel'   => 'ID Akun',
	'CharLogCharNumLabel'	 => 'Slot karakter',
	'CharLogCharNameLabel'	=> 'Nama Karakter',
	// - logdata/inter
	'InterLogTitle'			=> 'Daftar Log Interaksi',
	'InterLogHeading'		  => 'Log Interaksi',
	'InterLogNotFound'		 => 'Tidak ada log interaksi yang diteukan.',
	'InterLogDateLabel'		=> 'Tanggal/Jam',
	'InterLogLabel'			=> 'Log Interaksi',
	// - logdata/mvp
	'MVPLogTitle'			=> 'Daftar Log MVP',
	'MVPLogHeading'		  => 'Log MVP',
	'MVPLogNotFound'		 => 'Tidak ada log MVP yang ditemukan.',
	'MVPLogIDLabel'		  => 'ID Log MVP',
	'MVPLogDateLabel'		=> 'Tanggal/Jam',
	'MVPLogCharacterLabel'   => 'ID Karakter',
	'MVPLogMonsterLabel'	 => 'Monster MVP',
	'MVPLogPrizeLabel'	   => 'Hadiah MVP',
	'MVPLogExpLabel'		 => 'Exp. MVP',
	'MVPLogMapLabel'		 => 'Map',
	//		- logdata/npc
	'NPCLogTitle'			=> 'Daftar Log NPC',
	'NPCLogHeading'		  => 'Log NPC',
	'NPCLogNotFound'		 => 'Tidak ada log NPC yang ditemukan.',
	'NPCLogIDLabel'		  => 'ID NPC',
	'NPCLogDateLabel'		=> 'Tanggal/Jam',
	'NPCLogAccountIDLabel'   => 'ID Akun',
	'NPCLogCharIDLabel'	  => 'ID Karakter',
	'NPCLogCharNameLabel'	=> 'Nama Karakter',
	'NPCLogMapLabel'		 => 'Map',
	'NPCLogMsgLabel'		 => 'Pesan',
	// - logdata/zeny
	'ZenyLogTitle'			=> 'Dafatr Log Zeny',
	'ZenyLogHeading'		  => 'Log Zeny',
	'ZenyLogNotFound'		 => 'Tidak ada log Zeny yang ditenukan.',
	'ZenyLogDateLabel'		=> 'Tanggal/Jam',
	'ZenyLogCharacterLabel'   => 'Karakter',
	'ZenyLogSourceLabel'	  => 'Sumber',
	'ZenyLogTypeLabel'		=> 'Tipe',
	'ZenyLogAmountLabel'	  => 'Jumlah',
	'ZenyLogMapLabel'		 => 'Map',

	// Module: mail
	// - mail/index
	'MailerTitle'			 => 'Formulir Pesan (Form Mailer)',
	'MailerHeading'		   => 'Formulir Pesan',
	'MailerEnterToAddress'	=> 'Harap masukkan alamat tujuan.',
	'MailerEnterSubject'	  => 'Harap masukkan judul.',
	'MailerEnterBodyText'	 => 'Harap masukkan beberapa isi pesan.',
	'MailerEmailHasBeenSent'  => 'E-mail anda telah sukses dikirim ke %s.',
	'MailerFailedToSend'	  => 'Sistem pengiriman pesan gagal untuk mengirim e-mail. Mungkin terjadi karena kesalahan konfigurasi.',
	'MailerInfo'			  => 'Anda diperbolehkan menggunakan formulir pesan di bawah ini untuk mengirimkan e-mail menggunakan control panel.',
	'MailerFromLabel'		 => 'Dari',
	'MailerToLabel'		   => 'Tujuan',
	'MailerSubjectLabel'	  => 'Judul',
	'MailerBodyLabel'		 => 'isi',
	'MailerSelectTemplateLabel'	 => 'Pilih Template',

	// Module: main
	// - main/index
	'MainPageHeading'		 => 'Flux Control Panel',
	'MainPageInfo'			=> 'Jika Anda melihat halaman ini, berarti Flux telah berhasil diinstal.',
	'MainPageInfo2'		   => 'Yakin untuk mengubah halaman ini?',
	'MainPageStep1'		   => 'Membuka "%s" di teks editor.',
	'MainPageStep2'		   => 'Ubah file dari editor dan simpan perubahan.',
	'MainPageThanks'		  => 'Terima kasih telah menggunakan Flux!',
	'MainPageWelcome'	 => 'Selamat datang di %s!',
	// - main/pagenotfound
	'PageNotFoundTitle'	   => '404 Page Not Found',
	'PageNotFoundHeading'	 => 'Halaman Tidak Ditemukan',
	'PageNotFoundInfo'		=> 'Halaman yang diminta tidak ditemukan. Harap periksa kembali alamat yang dimasukkan.',
	// - main/preprocess
	'DisallowedDuringWoE'	 => 'Halaman yang diminta tidak dapat diakses ketika WoE.',

	// Module: monster
	// - monster/index
	// - monster/view

	// Module: purchase
	// - purchase/add
	// - purchase/cart
	// - purchase/checkout
	// - purchase/clear
	// - purchase/index
	// - purchase/pending
	// - purchase/remove

	// Module: ranking
	// - ranking/character
	// - ranking/guild
	// - ranking/zeny

	// Module: server
	// - server/info
	'ServerInfoTitle'		 => 'Informasi Server',
	'ServerInfoHeading'	   => 'Informasi Server',
	'ServerInfoText'		  => 'Di sini Anda akan menemukan berbagai informasi tentang server.',
	'ServerInfoSubHeading'	=> 'Informasi untuk %s',
	'ServerInfoSubHeading2'   => 'Informasi Class untuk %s',
	'ServerInfoAccountLabel'  => 'Akun',
	'ServerInfoCharLabel'	 => 'Karakter',
	'ServerInfoGuildLabel'	=> 'Guild',
	'ServerInfoPartyLabel'	=> 'Parties',
	'ServerInfoZenyLabel'	 => 'Zeny',
	// - server/status
	'ServerStatusTitle'	   => 'Status Server Saat Ini',
	'ServerStatusHeading'	 => 'Status Server',
	'ServerStatusInfo'		=> "Berikut ini adalah status dari tiap server.",
	'ServerStatusServerLabel' => 'Server',
	'ServerStatusLoginLabel'  => 'Login Server',
	'ServerStatusCharLabel'   => 'Character Server',
	'ServerStatusMapLabel'	=> 'Map Server',
	'ServerStatusOnlineLabel' => 'Pemain Online',
	'ServerStatusPeakLabel'   => 'Pemain Terbanyak',

	// Module: service
	// - service/tos
	'TermsTitle'			  => 'Syarat dan Ketentuan',
	'TermsHeading'			=> 'Syarat dan Ketentuan',
	'TermsInfo'			   => 'Harap dibaca sebelum Anda membuat akun!',
	'TermsInfo2'			  => "FOR CONTROL PANEL ADMINISTRATOR:  Anda harus menambahkan server ToS pada tampilan ini secara langsung. Lokasi dari file ini adalah: %s",

	// Module: unauthorized
	// - unauthorized/index
	'UnauthorizedTitle'	   => 'Tidak Diperbolehkan',
	'UnauthorizedHeading'	 => 'Tidak Diperbolehkan',
	'UnauthorizedInfo'		=> 'Anda tidak diperbolehkan untuk melihat halaman ini. <a href="%s">Kembali ke sebelumnya…</a>',

	// Module: woe
	// - woe/index
	'WoeTitle'				=> 'Jadwal WoE',
	'WoeHeading'			  => 'Jadwal War of Emperium',
	'WoeInfo'				 => 'Di bawah ini adalah jadwal WoE untuk %d. Jadwal berikut mungkin bisa berubah sewaktu-waktu.',
	'WoeServerTimeInfo'	   => 'Waktu server saat ini adalah:',
	'WoeServerLabel'		  => 'Server',
	'WoeTimesLabel'		   => 'Jadwal War of Emperium',
	'WoeNotScheduledInfo'	 => 'Tidak ada jadwal WoE saat ini.',

	// Module: contactform
	'CFTitleSubmit'						=> 'Hubungi Kami',

	// Module: News and Pages
	'CMSNewsHeader'	 => 'Pengumuman',
	'CMSPageHeader'	 => 'Content Management System',
	'CMSPageText'	 => 'Modul ini mengizinkan admin dan staff untuk membuat halaman pada website tanpa perlu pengetahuan tentang Flux/pemrograman. Sistem berita internal juga dapat ekspor untuk RSS Feed dengan mengubah pengaturan aplikasi.',
	'CMSNewsTitleError'	 => 'Judul Berita harus diisi!',
	'CMSNewsBodyError'	 => 'Isi Berita harus diisi!',
	'CMSPageTitleError'	 => 'Judul Halaman harus diisi!',
	'CMSPageBodyError'	 => 'Isi Halaman harus diisi!',
	'CMSPagePathError'	 => 'Lokasi Halaman harus diisi!',
	'CMSNewsAdded'	 => 'Berita telah ditambahkan',
	'CMSPagesAdded'	 => 'Halaman telah ditambahkan',
	'CMSNewsUpdated'	 => 'Berita telah diperbaruhi',
	'CMSPageUpdated'	 => 'Halaman telah diperbaruhi',
	'CMSNewsAddTitle'	 => 'Menambahkan isi beritaAdd a news item',
	'CMSPageAddTitle'	 => 'Membuat halaman baru',
	'CMSNewsEditTitle'	 => 'Mengubah berita',
	'CMSPageEditTitle'	 => 'Mengubah halaman',
	'CMSNewsNotFound'	 => 'Berita tidak ditemukan!',
	'CMSPageNotFound'	 => 'Halaman tidak ditemukan!',
	'CMSNewsDeleted'	 => 'Berita telah dihapus',
	'CMSPageDeleted'	 => 'Halamna telah dihapus',
	'CMSNewsEmpty'	 => 'Tidak ada bertia yang ditemukan. Cek kembali Tipe Berita di pengaturan? (pengaturan CMSNewsType)',
	'CMSNewsRSSNotFound'	 => "RSS feed tidak dapat ditemukan. Cek kembali pengaturan CMSNewsRSS, atau ganti tipe CMSNewsType menjadi 1 untuk menggunakan sistem berita internal!",
	'CMSPageEmpty'	 => 'Tidak ada halaman',
	'CMSNewsLink'	 => 'baca selengkapnya...',
	'CMSEdit'	 => 'Perbaruhi',
	'CMSDelete'	 => 'Hapus',
	'CMSNewsTitleLabel'	 => 'Judul Berita',
	'CMSNewsBodyLabel'	 => 'Isi Berita',
	'CMSNewsLinkLabel'	 => 'Tautan Berita',
	'CMSNewsAuthorLabel'	 => 'Penulis Berita',
	'CMSPageTitleLabel'	 => 'Judul Halaman',
	'CMSPageBodyLabel'	 => 'Isi Halaman',
	'CMSPagePathLabel'	 => 'Lokasi Halaman',
	'CMSCreatedLabel'	 => 'Tanggal Pembuatan',
	'CMSModifiedLabel'	 => 'Tanggal Diperbaruhi',
	'CMSActionLabel'	 => 'Tindakan',
	'CMSConfirmDeleteLabel'	 => 'Apakah yakin untuk menghapus?',
	'CMSPageCreate'	 => 'Apakah yakin membuat halaman?',
	'CMSOptionalLabel'	 => '(Opsional)',
	'CMSRequiredLabel'	 => '(Harus Diisi)',
	'CMSCreateLabel'	 => 'Buat Berita Baru',

	// Module: vending
	'TLHeaderTasks'						=> 'Tugas',
	'TLHeaderOwner'						=> 'Pemilik',
	'TLHeaderPriority'				=> 'Prioritas',
	'TLHeaderStatus'				=> 'Status',
	'TLHeaderCreated'				=> 'Dibuat',
	'TLHeaderModified'				=> 'Diubah',
	'TLHeaderResources'				=> 'Bahan Tambahan',
	'TLHeaderBody'						=> 'Isi',

	// Module: servicedesk
	'SDHeader'								=> 'Layanan Bantuan',
	'SDCreateNew'						=> 'Kirim Laporan',
	'SDWelcomeText'						=> 'Selamat datang di Layanan Bantuan',
	'SDNoTickets'						=> 'Anda tidak memiliki laporan apapun.',
	'SDNoBlankResponse'				=> 'Anda harus mengisi data ke dalam formulir!',
	'SDNoCatsAvailable'				=> 'Tidak ada kategori yang tersedia',
	'SDNoOpenTickets'				=> 'Tidak ada laporan di database.',
	'SDNoInactiveTickets'		=> "Tidak ada laporan yang 'tidak aktif' di database.",
	'SDNoClosedTickets'				=> "Tidak ada laporan yang 'ditutup' di database.",
	'SDNoCats'								=> 'Tidak ada kategori di database.',
	'SDHuh'										=> 'Error',
	'SDPointerChatLog'				=> 'Kami sarankan untuk menggunakan <a href="http://pastebin.com" target="_blank">pastebin.com</a> lalu kirimkan tautannya pada kami.',
	'SDPointerScreenShot'		=> 'Kirimkan juga tautan dari gambar atau screenshot yang Anda punya',
	'SDPointerVideoLink'		=> 'Kami sarankan untuk mengunggah video Anda ke YouTube, lalu kirimkan tautannya pada kami.',
	'SDHeaderID'						=> 'Laporan #',
	'SDHeaderSubject'				=> 'Subjek',
	'SDHeaderCategory'				=> 'Kategori',
	'SDHeaderStatus'				=> 'Status',
	'SDHeaderLastAuthor'		=> 'Penulis Terakhir',
	'SDHeaderTimestamp'				=> 'Dibuat',
	'SDHeaderAccount'				=> 'Akun',
	'SDHeaderTeam'						=> 'Tim',
	'SDH3ActiveTickets'				=> 'Laporan Aktif',
	'SDH3InActiveTickets'		=> 'Laporan Tidak Aktif',
	'SDH3ClosedTickets'				=> 'Laporan Ditutup',
	'SDH3CurrentCat'				=> 'Kategori',
	'SDH3CreateCat'						=> 'Buat Kategori Baru',
	'SDH3StaffList'						=> 'Pengaturan Staf',
	'SDH3StaffCreate'				=> 'Tambah Staf',
	'SDReOpenPlayer'				=> 'Laporan diaktifkan kembali oleh pemain',
	'SDReOpenStaff'						=> '',
	'SDRespTable1'						=> 'Balas dan Kembali ke Laporan',
	'SDRespTable2'						=> 'Balas dan Kembali ke Daftar Laporan',
	'SDRespTable3'						=> 'Balas dan Tutup Laporan',
	'SDRespTable4'						=> 'Balas dan Tingkatkan Status',
	'SDRespTable5'						=> 'Tutup Laporan',
	'SDRespTable6'						=> 'Balas dan Aktifkan Laporan',
	'SDRespTable7'	 => 'Resolve Ticket dan Credit Account',
	'SDGroup1'								=> 'Staf Layanan Bantuan',
	'SDGroup2'								=> 'Kepala Staf Layanan Bantuan',
	'SDGroup3'								=> 'Administrasi',
	'SDLinkOpenNew'						=> 'Buat laporan baru',

	// Module: webcommands
	'WCTitleLabel'		=> 'Web Commands',

);
?>
