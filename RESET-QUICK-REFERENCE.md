# 🔄 RagnaDocker Reset Quick Reference

## ⚡ Quick Commands

```bash
# Factory reset (interactive)
./reset-project.sh

# Factory reset via Makefile
make reset

# Rebuild after reset
./start.sh
# OR
make setup
```

## ⚠️ What Gets Deleted

| Component | Status | Details |
|-----------|--------|---------|
| rAthena source | ❌ DELETED | All downloaded source files |
| FluxCP source | ❌ DELETED | All downloaded source files |
| Database data | ❌ DELETED | Characters, accounts, guilds |
| Docker images | ❌ DELETED | All built project images |
| Docker volumes | ❌ DELETED | All persistent data |
| Log files | ❌ DELETED | All application logs |
| SQL backups | ❌ DELETED | All backup files |

## ✅ What Gets Preserved

| Component | Status | Details |
|-----------|--------|---------|
| Project structure | ✅ KEPT | All directories maintained |
| docker-compose.yml | ✅ KEPT | Main configuration |
| Custom Dockerfiles | ✅ KEPT | Your custom containers |
| Makefile | ✅ KEPT | Build automation |
| Documentation | ✅ KEPT | README, guides |
| Configuration files | ✅ KEPT | Custom configs |
| Scripts | ✅ KEPT | Helper scripts |

## 🛡️ Safety Checklist

- [ ] **Backup important data** (characters, custom configs)
- [ ] **Stop all services** before reset
- [ ] **Confirm you're in project root** directory
- [ ] **Read the warnings** carefully
- [ ] **Type confirmations** exactly as requested
- [ ] **Have rebuild plan** ready

## 🚀 Rebuild Process

1. **Reset**: `./reset-project.sh`
2. **Rebuild**: `./start.sh`
3. **Verify**: `make status`
4. **Access**: http://localhost:8080

## 🆘 Emergency Commands

```bash
# If reset fails, manual cleanup:
docker-compose down --remove-orphans
docker system prune -a --volumes
rm -rf rathena/* fluxcp/* logs/*

# Restore essential files:
git checkout rathena/Dockerfile fluxcp/Dockerfile

# Rebuild:
./start.sh
```

## 📞 Need Help?

- Read: `RESET-GUIDE.md` for detailed documentation
- Check: `README.md` for general setup instructions
- Run: `make help` for available commands

---
**⚠️ REMEMBER: Reset is IRREVERSIBLE! Always backup first! ⚠️**
